server:
  maxThreads: 128
  minThreads: 128
  applicationConnectors:
    - type: http
      port: 8080
  adminConnectors:
    - type: http
      port: 8081
  applicationContextPath: /
  requestLog:
    appenders:
      - type: console
        timeZone: IST

logging:
  level: INFO
  loggers:
    com.phonepe: INFO
    org.hibernate: DEBUG
  appenders:
    - type: console
      threshold: INFO
      timeZone: IST
      logFormat: "%(%-5level) [%date] [%thread] [%logger{0}]: %message%n"

discovery:
  namespace: phonepe
  environment: local
  zookeeper: stg-appzk001.phonepe.nb6:2181,stg-appzk002.phonepe.nb6:2181,stg-appzk003.phonepe.nb6:2181
  publishedHost: stg-appzk001.phonepe.nb6
  publishedPort: 8080

reporterConfig:
  prefix: phonepe.local.stratos
  pollingInterval: 30
  attributes:
    hostname: localhost:8080
    env: local
  cluster: riemann-influx001
  metricServiceConfig:
    namespace: phonepe
    logLevel: BASIC
    zookeeper: stg-appzk001.phonepe.nb6:2181,stg-appzk002.phonepe.nb6:2181,stg-appzk003.phonepe.nb6:2181
    httpConfiguration:
      usingZookeeper: true
      host: metricingestion.traefik.stg.phonepe.nb6
      serviceName: metricingestion
      clientId: metricingestion
      environment: stage
      connections: 10
      idleTimeOutSeconds: 30
  farm: NB6

eventIngestor:
  usingZookeeper: true
  host: events.traefik.stg.phonepe.nb6
  port: 80
  environment: stage
  serviceName: dp-ingestion-api
  namespace: phonepe
  clientType: queued
  queuePath: /tmp/stratos
  clientId: event
  farmId: NB6

hystrixConfig:
  defaultConfig:
    threadPool:
      timeout: 4000
      concurrency: 10

swagger:
  resourcePackage: "com.phonepe.merchant.platform.stratos.server.core.resources,com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.resources,com.phonepe.merchant.platform.stratos.server.spokes.toa.resources,com.phonepe.central.stratos.penalty.server.resources,com.phonepe.central.workflow.server.resources"

errorPropertiesPath:   stratos-server/src/main/resources/resourcebundle/

database:
  shards:
    - driverClass: org.mariadb.jdbc.Driver
      user: mcp_user_rw
      password: Joh7ohva@875
      url: *********************************************************************************************************
      properties:
        charSet: UTF-8
        hibernate.dialect: org.hibernate.dialect.MySQLDialect
        hibernate.hbm2ddl.auto: none
        hibernate.show_sql: true
        hibernate:format_sql: true
      maxWaitForConnection: 1s
      validationQuery: "/* MyApplication Health Check */ SELECT 1"
      validationQueryTimeout: 1s
      minSize: 12
      maxSize: 12
      initialSize: 12
      logAbandonedConnections: true
      logValidationErrors: true
      checkConnectionWhileIdle: true
      checkConnectionOnConnect: true
      checkConnectionOnBorrow: true
      removeAbandoned: true
      evictionInterval: 1m
      minIdleTime: 2m
      maxConnectionAge: 15m
      defaultTransactionIsolation: READ_COMMITTED
      useFairQueue: false
    - driverClass: org.mariadb.jdbc.Driver
      user: mcp_user_rw
      password: Joh7ohva@875
      url: *********************************************************************************************************
      properties:
        charSet: UTF-8
        hibernate.dialect: org.hibernate.dialect.MySQLDialect
        hibernate.hbm2ddl.auto: none
        hibernate.show_sql: true
        hibernate:format_sql: true
      maxWaitForConnection: 1s
      validationQuery: "/* MyApplication Health Check */ SELECT 1"
      validationQueryTimeout: 1s
      minSize: 12
      maxSize: 12
      initialSize: 12
      logAbandonedConnections: true
      logValidationErrors: true
      checkConnectionWhileIdle: true
      checkConnectionOnConnect: true
      checkConnectionOnBorrow: true
      removeAbandoned: true
      evictionInterval: 1m
      minIdleTime: 2m
      maxConnectionAge: 15m
      defaultTransactionIsolation: READ_COMMITTED
      useFairQueue: false

validationConfig:
  headerValidationConfig:
    validateRelativePathValue: true
    headersVsForbiddenRegexList:
      X-DEVICE-FINGERPRINT:
        - \+.\/
        - \.+\\
  pathValidationConfig:
    validateHttpRelativePath: true
    forbiddenRegexList:
      - \.+\/
      - \.+\\

notificationClientConfig:
  emailConfig:
    emailServiceConfig:
      clientId: ZencastClient
      usingZookeeper: true
      serviceName: zencast
      environment: stage
      host: zencast.traefik.stg.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
      connectTimeoutMs: 10000
      opTimeoutMs: 10000
    sender: <EMAIL>
    senderName: phonepe
    profileId:  BUSINESS_INFORMATIONAL
    hystrixDisabled: true


killswitchConfig:
  projectId: stratos
  toaHoldTemplateId: stratos-toa_hold
  toaHoldKillSwitchApplyDurationInSec: 900

tstoreClientConfig:
  namespace: stratos
  tstoreConfiguration:
    host: tstore.nixy.stg-drove.phonepe.nb6
    connectTimeoutMs: 60000
    connections: 10
    clientId: tstoreClient
    usingDiscovery: false
    environment: stage
    port: 80
    serviceName: tstore
  tstoreBcpConfiguration:
    host: tstore-bcp.nixy.stg-drove.phonepe.nb6
    connectTimeoutMs: 60000
    connections: 10
    clientId: tstoreBcpClient
    usingDiscovery: false
    environment: stage
    port: 80
    serviceName: tstoreBcp
  schemaConfiguration:
    host: schema-server.nixy.stg-drove.phonepe.nb6
    connectTimeoutMs: 60000
    port: 80
    connections: 10
    clientId: schemaConfigurationClient
    usingDiscovery: true
    environment: stage
    serviceName: schema-registry

primerBundleConfig:
  enabled: true
  authTypesEnabled:
    CONFIG: false
    ANNOTATION: true
  absentTokenStatus: UNAUTHORIZED
  endpoint:
    type: rangerhub
    clientId: primer
    namespace: phonepe
    service: primer
    environment: stage
    zookeeper: stg-appzk001.phonepe.nb6:2181,stg-appzk002.phonepe.nb6:2181,stg-appzk003.phonepe.nb6:2181
  cacheExpiry: 60
  cacheMaxSize: 100000
  clockSkew: 60
  prefix: Bearer
  privateKey: TwhjV5ujkvb41frpmqCve7ZfhqwSDMqOXe01DeDIsb2xCrW4bwfFnax9bi2uC9Kn

kratosConfiguration:
  chargebackAcceptanceType: CHARGEBACK_ACCEPTANCE_CHECK
  ruleEngineClientConfiguration:
    schemaValidationDisabled: false
    httpConfig:
      clientId: kratos
      environment: stage.stable
      serviceName: kratos
      secure: false
      usingZookeeper: true
      host: kratos.nixy.stg-drove.phonepe.nb6
      port: 80
      namespace: phonepe
      connections: 10
      idleTimeOutSeconds: 30
      connectTimeoutMs: 10000
      opTimeoutMs: 5000
    tenant: stratos
    hystrixEnabled: true
    fraudActionImplementationPath: com.phonepe.merchant.platform.stratos.server.core.clients.kratos
    retryerConfig:
      minWaitMS: 10
      maxWaitMS: 50
      maxAttempts: 1

penaltyTenantConfigs:
  - tenantName : 'DISPUTE'
    tenantSubCategoryName: 'UPI'
    tenantVersionConfigs:
      - version: 19
        disbursementConfig:
          type: MERCHANT_PENALTY
          disbursementMode: ToA
          upiPayProfile: MERCHANT_TOA
          destination:
            type: EGV
            programId: "TOA_VO"
            giftCardDestinationType: "EGV_USER_VAULT"
          userTargetType: ACCOUNT
          transferUseCase: BBPS_PENALTY
        penaltyRecoveryConfig:
          type: MERCHANT_PENALIZED_ENTITY
          merchantId: 'BBPSBP'
          mcc: '1234'
        clientPenaltyResolutionConfig:
          type: PASS_THROUGH_PROBABLE
  - tenantName : 'NEXUS'
    tenantSubCategoryName: 'BBPS'
    tenantVersionConfigs:
      - version: 1
        disbursementConfig:
          type: MERCHANT_PENALTY
          disbursementMode: ToA
          upiPayProfile: MERCHANT_TOA
          userTargetType: ACCOUNT
          destination:
            type: EGV
            programId: "TOA_VO"
            giftCardDestinationType: "EGV_USER_VAULT"
          transferUseCase: BBPS_PENALTY
        penaltyRecoveryConfig:
          type: MERCHANT_PENALIZED_ENTITY
          merchantId: 'BBPSBP'
          mcc: '1234'
        clientPenaltyResolutionConfig:
          type: SERVICE_CLIENT
          guiceClientClass: 'com.phonepe.central.stratos.penalty.server.client.NexusClient'
          evaluationCriteria:
            form: 'DNF'
            id: 'trigger'
            conjunctions:
              - type: 'AND'
                predicates:
                  - type: 'INCLUDED'
                    lhs: '$.state'
                    detail:
                      caveat: 'EQUALITY'
                      values:
                        - 'SUCCESS'
                        - 'PENDING'


rangerHubConfiguration:
  type: ZK
  namespace: phonepe
  nodeRefreshTimeMs: 5000
  watchDisabled: false
  services:
    - clientId: pg-transport
      authToken: Bearer eyJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJQaG9uZVBlUGdUcmFuc3BvcnQiLCJqdGkiOiIwNzQxNzZhMy1hM2YwLTRlNzItOTM5Mi0zNmRiMTExMzdjYmYiLCJpYXQiOjE1MDMzMTMxNjMsIm5iZiI6MTUwMzMxMzA0Mywic3ViIjoicGF5bWVudHMiLCJyb2xlIjoiYWRtaW4ifQ.b5gv4eFhqOlehbkvcG7RmcLIh9OEvEMd1eqbfrEFVEuZCK2K01Adc2RXbHN36kmV_Z90GWktCdkA5VhJ8Gu5ew
      serviceName: pg-transport
      environment: stage
      host: pg-transport.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      secure: false
      idleTimeOutSeconds: 30
      usingDiscovery: true
      usingRegistry: false
    - clientId: plutus-transaction-status
      serviceName: plutus-transaction-status
      environment: stage
      host: plutus-transaction-status.traefik.stg.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
      usingDiscovery: true
      usingRegistry: false
    - clientId: plutus-events-ingestion
      serviceName: plutus-events-ingestion
      environment: stage
      host: plutus-events-ingestion.traefik.stg.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
      usingDiscovery: true
      usingRegistry: false
    - clientId: merchant-service
      serviceName: merchant-service
      environment: stage
      host: merchant-service.traefik.stg.phonepe.nb6
      port: 80
      connections: 10
      secure: false
      idleTimeOutSeconds: 30
      usingDiscovery: true
      usingRegistry: false
    - clientId: MandateClient
      serviceName: merchant-mandates
      environment: stage
      host: merchant-mandates.traefik.stg.phonepe.com
      connections: 10
      port: 80
      connectTimeoutMs: 10000
      opTimeoutMs: 5000
      idleTimeOutSeconds: 30
      usingDiscovery: true
      usingRegistry: false
    - clientId: payments-txnl
      usingDiscovery: true
      usingRegistry: false
      serviceName: payments-txnl
      environment: stage
      host: payments-txnl.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      secure: false
      idleTimeOutSeconds: 30
      authIssuer: OLYMPUS
    - clientId: nexusClient
      usingDiscovery: true
      usingRegistry: false
      serviceName: nexusHttpClient
      environment: stage
      host: payments-txnl.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      secure: false
      idleTimeOutSeconds: 30
      authIssuer: OLYMPUS
    - clientId: payments-txnl-ppi
      usingZookeeper: false
      serviceName: payments-txnl-ppi
      environment: stage
      port: 80
      connections: 10
      secure: false
      idleTimeOutSeconds: 30
      authIssuer: OLYMPUS
    - clientId: stratosDocstore
      serviceName: phonepe-docstore
      environment: stage
      host: stg-docstore.phonepe.nb6
      port: 80
      connections: 10
      secure: false
      idleTimeOutSeconds: 30
      usingDiscovery: true
      usingRegistry: false
    - clientId: killswitches
      serviceName: killswitch
      environment: stage
      host: killswitch-server.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      secure: false
      idleTimeOutSeconds: 30
      usingDiscovery: true
      usingRegistry: false
    - clientId: edcServiceClient
      serviceName: edcService
      environment: stage
      host: edcservice.nixy.stg-drove.phonepe.nb6
      connections: 10
      port: 80
      connectTimeoutMs: 1000
      opTimeoutMs: 5000
      idleTimeOutSeconds: 30
      usingDiscovery: true
      usingRegistry: false
    - clientId: netpeServiceClient
      serviceName: netpe
      environment: stage
      host: api-internal.nixy.stg-drove.phonepe.nb6
      rootPathPrefix: "apis/netpe"
      connections: 10
      port: 80
      connectTimeoutMs: 1000
      opTimeoutMs: 5000
      idleTimeOutSeconds: 30
      usingDiscovery: false
      usingRegistry: false
    - clientId: refund-orchestrator
      serviceName: refund-orchestrator
      environment: stage
      host: refund-orchestrator.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      secure: false
      idleTimeOutSeconds: 30
      usingDiscovery: true
      usingRegistry: false
    - clientId: event
      serviceName: dp-ingestion-api
      environment: stage
      host: events.nixy.stg-drove.phonepe.nb6=
      port: 80
      connections: 10
      secure: false
      idleTimeOutSeconds: 30
      authIssuer: OLYMPUS
      usingDiscovery: false
      usingRegistry: false
    - clientId: kaizenKillswitch
      serviceName: killswitch
      environment: stage
      host: killswitch-server.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      secure: false
      idleTimeOutSeconds: 30
      usingDiscovery: true
      usingRegistry: false
    - clientId: kaizenDocstore
      serviceName: phonepe-docstore
      environment: stage
      host: dummy-kaizen.phonepe.nb6
      port: 80
      connections: 10
      secure: false
      idleTimeOutSeconds: 30
      usingDiscovery: false
      usingRegistry: false
    - clientId: sentinel
      host: dummy-kaizen.phonepe.nb6
      port: 443
      serviceName: sentinel
      environment: stage
      connections: 10
      idleTimeOutSeconds: 30
      usingDiscovery: false
      usingRegistry: false
    - clientId: atlas
      host: dummy-kaizen.phonepe.nb6
      #rootPathPrefix: apis/atlas
      port: 443
      #secure: true
      serviceName: atlas
      environment: stage
      connections: 10
      idleTimeOutSeconds: 30
      usingDiscovery: false
      usingRegistry: false
    - clientId: catalogue-service
      host: dummy-kaizen.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
      usingDiscovery: false
      usingRegistry: false
    - clientId: clockwork
      serviceName: clockwork-server
      environment: stage
      host: clockwork.nixy.stg-drove.phonepe.nb6
      port: 80
      secure: false
      connections: 10
      idleTimeOutSeconds: 30
      connectTimeoutMs: 10000
      opTimeoutMs: 10000
      usingDiscovery: false
      usingRegistry: false
    - clientId: drishti
      host: dummy-kaizen.phonepe.nb6
      port: 443
      secure: true
      serviceName: drishti
      environment: stage
      connections: 10
      idleTimeOutSeconds: 30
      usingDiscovery: false
      usingRegistry: false
    - clientId: geolocation
      host: dummy-kaizen.phonepe.nb6
      port: 443
      secure: true
      serviceName: atlasSecondary
      environment: stage
      connections: 10
      idleTimeOutSeconds: 30
      usingDiscovery: false
      usingRegistry: false
    - clientId: primer
      host: dummy-kaizen.phonepe.nb6
      port: 443
      serviceName: primer
      environment: stage
      connections: 10
      idleTimeOutSeconds: 30
      usingDiscovery: false
      usingRegistry: false
    - clientId: api
      host: dummy-kaizen.phonepe.nb6
      port: 443
      secure: true
      serviceName: api
      environment: stage
      connections: 10
      idleTimeOutSeconds: 30
      usingDiscovery: false
      usingRegistry: false
    - clientId: warden
      usingDiscovery: true
      serviceName: warden
      host: warden.nixy.stg-drove.phonepe.nb6
      port: 80
      environment: stage
      connections: 10
      idleTimeOutSeconds: 30
      opTimeoutMs: 30000
      connectTimeoutMs: 2000
    - clientId: StratosZencastClient
      usingZookeeper: true
      serviceName: zencast
      environment: stage
      host: zencast.traefik.stg.phonepe.nb6
      port: 80
      idleTimeOutSeconds: 30
      connectTimeoutMs: 10000
      opTimeoutMs: 10000

rmqConfig:
  brokers:
    - host: stg-rmqpp001.phonepe.nb6
      port: 5671
    - host: stg-rmqpp002.phonepe.nb6
      port: 5671
    - host: stg-rmqpp003.phonepe.nb6
      port: 5671
  threadPoolSize: 32
  userName: admin
  password: admin
  secure: true

stratosActorsConfig:
  FILE_PROCESSOR:
    exchange: ${USER}.file_processor
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  FILE_ROW_PROCESSOR:
    exchange: ${USER}.file_row_processor
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  UPI_FIRST_LEVEL_CHARGEBACK_CREATION_HANDLER:
    exchange: ${USER}.upi_first_level_chargeback_creation_handler
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  RAISE_CHARGEBACK_RECOVERY_ACCOUNTING_EVENT:
    exchange: ${USER}.raise_chargeback_recovery_accounting_event
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  CHECK_CHARGEBACK_RECOVERY_ACCOUNTING_EVENT_STATUS:
    exchange: ${USER}.check_chargeback_recovery_accounting_event_status
    delayed: true
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 6
      waitTime: 10m
  CHARGEBACK_RECOVERY_FEED_PUBLISHER:
    exchange: ${USER}.chargeback_recovery_feed_publisher
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  RAISE_CHARGEBACK_RECOVERY_REVERSAL_ACCOUNTING_EVENT:
    exchange: ${USER}.raise_chargeback_recovery_reversal_accounting_event
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  CHECK_CHARGEBACK_RECOVERY_REVERSAL_ACCOUNTING_EVENT_STATUS:
    exchange: ${USER}.check_chargeback_recovery_reversal_accounting_event_status
    delayed: true
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 6
      waitTime: 10m
  CHARGEBACK_RECOVERY_REVERSAL_FEED_PUBLISHER:
    exchange: ${USER}.chargeback_recovery_reversal_feed_publisher
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  PG_FIRST_LEVEL_CHARGEBACK_CREATION_HANDLER:
    exchange: ${USER}.pg_first_level_chargeback_creation_handler
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  PG_MIS_ROW_PROCESSOR:
    exchange: ${USER}.pg_mis_row_processor
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  TOA_PROCESSOR_HANDLER:
    exchange: ${USER}.toa_processor_handler
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  P2PM_TOA_CREATION_HANDLER:
    exchange: ${USER}.p2pm_toa_creation_handler
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  P2PM_TOA_PAY_STATUS_CHECK_HANDLER:
    exchange: ${USER}.p2pm_toa_pay_status_check_handler
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  P2PM_TOA_PAY_RETRY_HANDLER:
    exchange: ${USER}.p2pm_toa_pay_retry_handler
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  EDC_CHARGEBACK_CREATION_HANDLER:
    exchange: ${USER}.edc_chargeback_creation_handler
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  EDC_ROW_PROCESSOR:
    exchange: ${USER}.edc_row_processor
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  KRATOS_WORKFLOW_PROCESSOR:
    exchange: ${USER}.kratos_worker_processor
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  CREATE_TOA_HANDLER:
    exchange: ${USER}.toa_creation_handler
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  NETBANKING_FIRST_LEVEL_CHARGEBACK_CREATION_HANDLER:
    exchange: ${USER}.netbanking_first_level_chargeback_creation_handler
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  STATE_CHANGE_HANDLER:
    exchange: ${USER}.state_change_handler
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  TOA_PAY_STATUS_CHECK_HANDLER:
    exchange: ${USER}.toa_pay_status_check_handler
    delayed: true
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  TOA_PAY_RETRY_HANDLER:
    exchange: ${USER}.toa_pay_retry_handler
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  AUTO_APPROVAL_HANDLER:
    exchange: ${USER}.auto_approval_handler
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  CALLBACK_HANDLER:
    exchange: ${USER}.callback_handler
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s
  ESCALATED_NOTIFICATION_HANDLER:
    exchange: ${USER}.escalated_notification_handler
    delayed: false
    prefix: ${USER}.stratos
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_FIXED_WAIT
      maxAttempts: 5
      waitTime: 5s

fileConfigs:
  UPI_CHARGEBACK_YES:
    fileFormat:
      type: CSV
      lookUpKey: HEADER

    schema:
      fields:
        - type: DATE
          format: dd-MM-yyyy
          name: Adjdate
          required: true
        - type: DATE
          format: dd-MM-yyyy
          name: Txndate
          required: true
        - type: STRING
          name: Adjtype
          required: true
          filterExpression: 'arr.in("$.Adjtype", ["Chargeback Raise", "Re-presentment Raise", "Pre-Arbitration Raise", "Pre-Arbitration Acceptance", "Fraud Chargeback Raise", "Fraud Chargeback Representment", "Differed Chargeback Raise", "Differed Re-presentment Raise", "Differed Pre-Arbitration Acceptance", "Differed Pre-Arbitration Raise"]) == true'
        - type: STRING
          name: Remitter
          required: true
        - type: STRING
          name: Beneficiery
          required: true
          filterExpression: '"$.Beneficiery" == "YES"'
        - type: STRING
          name: RRN
          required: true
        - type: DOUBLE
          name: Txnamount
          required: true
        - type: DOUBLE
          name: Adjamount
          required: true
        - type: DOUBLE
          name: Compensation amount
          required: true
        - type: STRING
          name: UPI Transaction ID
          required: true

    disputeFileConfig:
      disputeWorkflowConfig: >-
        {
        	"disputeStage" : "/{/{translateTxt op_chargeback_raise='FIRST_LEVEL' op_fraud_chargeback_raise='FIRST_LEVEL' op_fraud_chargeback_representment='FIRST_LEVEL' op_re_presentment_raise='FIRST_LEVEL' op_pre_arbitration_raise = 'PRE_ARBITRATION' op_pre_arbitration_acceptance='PRE_ARBITRATION' op_differed_chargeback_raise='FIRST_LEVEL' op_differed_re_presentment_raise='FIRST_LEVEL' op_differed_pre_arbitration_raise = 'PRE_ARBITRATION' op_differed_pre_arbitration_acceptance = 'PRE_ARBITRATION' pointer='/Adjtype'/}/}",
        	"currentState" : "/{/{translateTxt op_chargeback_raise='RECEIVED' op_fraud_chargeback_raise='RECEIVED' op_fraud_chargeback_representment='CREDIT_RECEIVED' op_re_presentment_raise='CREDIT_RECEIVED' op_pre_arbitration_raise = 'RECEIVED' op_pre_arbitration_acceptance='DEBIT_RECEIVED' op_differed_chargeback_raise='RECEIVED' op_differed_re_presentment_raise='CREDIT_RECEIVED' op_differed_pre_arbitration_raise = 'RECEIVED' op_differed_pre_arbitration_acceptance = 'DEBIT_RECEIVED' pointer='/Adjtype'/}/}",
            "currentEvent" : "/{/{translateTxt op_chargeback_raise='CREATE_ENTRY' op_fraud_chargeback_raise='CREATE_ENTRY' op_fraud_chargeback_representment='RECEIVE_CREDIT' op_re_presentment_raise='RECEIVE_CREDIT' op_pre_arbitration_raise = 'CREATE_ENTRY' op_pre_arbitration_acceptance='RECEIVE_DEBIT' op_differed_chargeback_raise='CREATE_ENTRY' op_differed_re_presentment_raise='RECEIVE_CREDIT' op_differed_pre_arbitration_raise = 'CREATE_ENTRY' op_differed_pre_arbitration_acceptance = 'RECEIVE_DEBIT' pointer='/Adjtype'/}/}",
        	"raisedAt" : "/{/{localDateToDateTime  pointer='/Adjdate'/}/}",
            "disputedAmount" : /{/{rupeesToPaise pointer='/Adjamount'/}/},
            "penaltyAmount" : /{/{rupeesToPaise pointer='/Compensation amount'/}/},
        	"instrumentTransactionId" : "/{/{[UPI Transaction ID]/}/}"
        }
      disputeConfig: >-
        {
            "currentDisputeStage" : "/{/{translateTxt op_chargeback_raise='FIRST_LEVEL' op_fraud_chargeback_raise='FIRST_LEVEL' op_fraud_chargeback_representment='FIRST_LEVEL' op_re_presentment_raise='FIRST_LEVEL' op_pre_arbitration_raise = 'PRE_ARBITRATION' op_pre_arbitration_acceptance='PRE_ARBITRATION' op_differed_chargeback_raise='FIRST_LEVEL' op_differed_re_presentment_raise='FIRST_LEVEL' op_differed_pre_arbitration_raise = 'PRE_ARBITRATION' op_differed_pre_arbitration_acceptance = 'PRE_ARBITRATION' pointer='/Adjtype'/}/}",
            "instrumentTransactionId" : "/{/{[UPI Transaction ID]/}/}",
            "transactionAmount" : /{/{rupeesToPaise pointer='/Txnamount'/}/},
            "rrn" : "/{/{replace [RRN] "'" ""/}/}",
            "disputeCategory": "/{/{translateTxt op_chargeback_raise='SERVICE_CHARGEBACK' op_fraud_chargeback_raise='FRAUD_CHARGEBACK' op_fraud_chargeback_representment='FRAUD_CHARGEBACK' op_pre_arbitration_raise='SERVICE_CHARGEBACK' op_pre_arbitration_acceptance='SERVICE_CHARGEBACK' op_re_presentment_raise='SERVICE_CHARGEBACK' op_differed_chargeback_raise='DIFFERED_CHARGEBACK' op_differed_re_presentment_raise='DIFFERED_CHARGEBACK' op_differed_pre_arbitration_raise = 'DIFFERED_CHARGEBACK' op_differed_pre_arbitration_acceptance = 'DIFFERED_CHARGEBACK'  pointer='/Adjtype'/}/}",
            "disputeIssuer": "YES_NPCI"
        }
      metaConfig: >-
        {
            "type": "UPI_CHARGEBACK",
            "transactionDate": "/{/{localDateToDateTime  pointer='/Txndate'/}/}",
            "utr" : "/{/{replace [RRN] "'" ""/}/}"
        }

    disputeStageTTLDaysMap:
      FIRST_LEVEL: 15
      PRE_ARBITRATION: 7

  PG_CHARGEBACK_PG_FIRST_LEVEL:
    fileFormat:
      type: CSV
      lookUpKey: HEADER

    schema:
      fields:
        - name: CB Received Date
          type: DATE
          format: dd-MM-yyyy
          required: true
        - name: Source
          type: STRING
          required: true
          pattern: "^(HDFC Bank|Razor Pay|Pay U|Axis Bank|Bill Desk|First Data|TPSL|PayNexT|ATOM|LYRA|KOTAK|NETPE_SBIN|NETPE_AXIS|ICICI)$"
        - name: Bank Ref No
          type: STRING
          required: true
          pattern: "^[a-zA-Z0-9]+$"
        - name: Bank Disputed Amount
          type: DOUBLE
          required: true
        - name: SM Transaction id(SRC_PRN)
          type: STRING
          required: true
          pattern: "^PG[0-9]+$"
        - name: PG_ID(RET_BID)
          type: STRING
          required: true
          pattern: "^[a-zA-Z0-9]+$"
        - name: Reason Code
          type: STRING
          pattern: "^[A-Z0-9.]*$"
        - name: Dispute Reason
          type: STRING
          required: true
          pattern: "^(Service Chargeback|Fraud Chargeback|Duplicate Chargeback)$"

    disputeFileConfig:
      disputeWorkflowConfig: >-
        {
        	"disputeStage" : "FIRST_LEVEL",
        	"currentState" : "RECEIVED",
            "currentEvent" : "CREATE_ENTRY",
            "disputedAmount" : /{/{rupeesToPaise pointer='/Bank Disputed Amount'/}/},
            "raisedAt" : "/{/{localDateToDateTime pointer='/CB Received Date'/}/}"
        }
      disputeConfig: >-
        {
            "currentDisputeStage" : "FIRST_LEVEL",
            "instrumentTransactionId" : "/{/{[SM Transaction id(SRC_PRN)]/}/}",
            "disputeReferenceId": "/{/{[Bank Ref No]/}/}",
            "rrn" : "/{/{[PG_ID(RET_BID)]/}/}",
            "disputeCategory": "/{/{reasonCodeToDisputeCategory op_service_chargeback='SERVICE_CHARGEBACK' op_fraud_chargeback='FRAUD_CHARGEBACK' op_duplicate_chargeback='DUPLICATE_CHARGEBACK' pointer_1='/Reason Code' pointer_2='/Dispute Reason'/}/}",
            "disputeIssuer": "/{/{translateTxt op_hdfc_bank='HDFC_PG' op_razor_pay='RAZORPAY_PG' op_pay_u='PAYU_PG' op_axis_bank='AXISBANK_PG' op_bill_desk='BILLDESK_PG' op_first_data='FIRSTDATA_PG' op_tpsl='TPSL_PG' op_paynext='PAYNEXT' op_atom='ATOM' op_lyra='LYRA' op_kotak='KOTAK' op_netpe_sbin='NETPE_SBIN' op_netpe_axis='NETPE_AXIS' op_icici='ICICI' pointer='/Source'/}/}"
        }
      metaConfig: >-
        {
            "type": "PG_CHARGEBACK"
        }

    disputeStageTTLDaysMap:
      FIRST_LEVEL: 4
      PRE_ARBITRATION: 2

  PG_CHARGEBACK_PG_PRE_ARBITRATION:
    fileFormat:
      type: CSV
      lookUpKey: HEADER

    schema:
      fields:
        - name: CB Received Date
          type: DATE
          format: dd-MM-yyyy
          required: true
        - name: Source Of CB
          type: STRING
          required: true
          pattern: "^(HDFC Bank|Razor Pay|Pay U|Axis Bank|Bill Desk|First Data|TPSL)$"
        - name: Amount
          type: DOUBLE
          required: true
        - name: Transaction ID
          type: STRING
          required: true
          pattern: "^PG[0-9]+$"

    disputeFileConfig:
      disputeWorkflowConfig: >-
        {
        	"disputeStage" : "PRE_ARBITRATION",
        	"currentState" : "RECEIVED",
            "currentEvent" : "CREATE_ENTRY",
            "disputedAmount" : /{/{rupeesToPaise pointer='/Amount'/}/},
            "raisedAt" : "/{/{localDateToDateTime pointer='/CB Received Date'/}/}"
        }
      disputeConfig: >-
        {
            "currentDisputeStage" : "PRE_ARBITRATION",
            "instrumentTransactionId" : "/{/{[Transaction ID]/}/}",
            "disputeIssuer": "/{/{translateTxt op_hdfc_bank='HDFC_PG' op_razor_pay='RAZORPAY_PG' op_pay_u='PAYU_PG' op_axis_bank='AXISBANK_PG' op_bill_desk='BILLDESK_PG' op_first_data='FIRSTDATA_PG' op_tpsl='TPSL_PG' pointer='/Source Of CB'/}/}"
        }
      metaConfig: >-
        {
            "type": "PG_CHARGEBACK"
        }

    disputeStageTTLDaysMap:
      FIRST_LEVEL: 4
      PRE_ARBITRATION: 2

  EDC_CHARGEBACK_EDC_FIRST_LEVEL:
    #fileNamePattern: "^.*([0]?[1-9]|[1|2][0-9]|[3][0|1])[-]([0]?[1-9]|[1][0-2])[-]([0-9]{4}).csv"

    fileFormat:
      type: CSV
      lookUpKey: HEADER

    schema:
      fields:
        - name: CB Received Date
          type: DATE
          format: dd-MM-yyyy
          required: true
        - name: Source
          type: STRING
          required: true
          pattern: "^(Axis Bank)$"
        - name: Tenant
          type: STRING
          required: true
          pattern: "^(BijliPay)$"
        - name: External Terminal Id
          type: STRING
          required: true
          pattern: "^[a-zA-Z0-9_]+$"
        - name: External Merchant Id
          type: STRING
          required: true
          pattern: "^[a-zA-Z0-9_]+$"
        - name: Bank Disputed Amount
          type: DOUBLE
          required: true
        - name: RRN
          type: STRING
          required: true
          pattern: "^[a-zA-Z0-9]+$"
        - name: Dispute Reason
          type: STRING
          required: true
          pattern: "^(Service Chargeback)$"

    disputeFileConfig:
      disputeWorkflowConfig: >-
        {
            "disputeStage" : "FIRST_LEVEL",
            "currentState" : "RECEIVED",
            "currentEvent" : "CREATE_ENTRY",
            "raisedAt" : "/{/{localDateToDateTime pointer='/CB Received Date'/}/}",
            "disputedAmount" : /{/{rupeesToPaise pointer='/Bank Disputed Amount'/}/}
        }
      disputeConfig: >-
        {
            "currentDisputeStage" : "FIRST_LEVEL",
            "instrumentTransactionId" : "/{/{[External Terminal Id]/}/}",
            "rrn" : "/{/{replace [RRN] "'" ""/}/}",
            "disputeCategory":"/{/{translateTxt op_service_chargeback='SERVICE_CHARGEBACK' pointer='/Dispute Reason'/}/}",
            "disputeIssuer": "/{/{translateTxt  op_axis_bank='AXISBANK_PG' pointer='/Source'/}/}"
        }
      metaConfig: >-
        {
            "type": "EDC_CHARGEBACK",
            "utr" : "/{/{replace [RRN] "'" ""/}/}",
            "terminalId": "/{/{[External Terminal Id]/}/}",
            "merchantId": "/{/{[External Merchant Id]/}/}",
            "tenant": "/{/{[Tenant]/}/}"
        }

    disputeStageTTLDaysMap:
      FIRST_LEVEL: 4

  NB_CHARGEBACK_NB_FIRST_LEVEL:
    disputeFileConfig:
      disputeWorkflowConfig: >-
        {
            "disputeStage" : "FIRST_LEVEL",
            "currentState" : "RECEIVED",
            "currentEvent" : "CREATE_ENTRY",
        	"raisedAt" : "/{/{stringToLocalDateTime  pointer='/CB Received Date' date_pattern='yy/MM/dd'}/}",
            "disputedAmount" : /{/{rupeesToPaise pointer='/Amount'/}/}
        }
      disputeConfig: >-
        {
            "currentDisputeStage" : "FIRST_LEVEL",
            "instrumentTransactionId" : "/{/{[Transaction ID]/}/}",
            "disputeCategory": "/{/{translateTxt op_service_chargeback='SERVICE_CHARGEBACK' op_fraud_chargeback='FRAUD_CHARGEBACK' pointer='/Dispute Reason'/}/}",
            "disputeIssuer": "/{/{[Source]/}/}",
            "rrn": "/{/{[Bank Reference ID]/}/}"
        }
      metaConfig: >-
        {
            "type": "NB_CHARGEBACK",
            "netBankingSourceType" : "/{/{sourceIDToSourceType pointer='/Transaction ID'/}/}"
        }

    disputeStageTTLDaysMap:
      FIRST_LEVEL: 4

  WALLET_CHARGEBACK_PHP:
    fileFormat:
      type: CSV
      lookUpKey: HEADER

    schema:
      fields:
        - type: DATE
          format: dd-MM-yyyy
          name: Adjdate
          required: true
        - type: DATE
          format: dd-MM-yyyy
          name: Txndate
          required: true
        - type: STRING
          name: Adjtype
          required: true
          filterExpression: 'arr.in("$.Adjtype", ["Chargeback Raise", "Re-presentment Raise", "Chargeback Acceptance", "Pre-Arbitration Raise", "Pre-Arbitration Acceptance", "Pre-Arbitration Declined", "Fraud Chargeback Raise", "Fraud Chargeback Representment", "Fraud Chargeback Acceptance"]) == true'
        - type: STRING
          name: Remitter
          required: true
          filterExpression: '"$.Remitter" == "PHP"'
        - type: STRING
          name: Beneficiery
          required: true
        - type: STRING
          name: RRN
          pattern: "^[0-9]+$"
          required: true
        - type: DOUBLE
          name: Txnamount
          required: true
        - type: DOUBLE
          name: Adjamount
          required: true
        - type: DOUBLE
          name: Compensation amount
          required: true
        - type: STRING
          name: UPI Transaction ID
          required: true

    disputeFileConfig:
      disputeWorkflowConfig: >-
        {
        	"disputeStage" : "/{/{translateTxt op_chargeback_raise='FIRST_LEVEL' op_fraud_chargeback_raise='FIRST_LEVEL' op_fraud_chargeback_acceptance='FIRST_LEVEL' op_fraud_chargeback_representment='FIRST_LEVEL' op_re_presentment_raise='FIRST_LEVEL' op_chargeback_acceptance='FIRST_LEVEL' op_pre_arbitration_raise = 'PRE_ARBITRATION' op_pre_arbitration_acceptance='PRE_ARBITRATION' op_pre_arbitration_declined='PRE_ARBITRATION' pointer='/Adjtype'/}/}",
        	"currentState" : "/{/{translateTxt op_chargeback_raise='NPCI_ACK_CHARGEBACK' op_fraud_chargeback_raise='NPCI_ACK_CHARGEBACK' op_fraud_chargeback_acceptance='MERCHANT_ACCEPTED_CHARGEBACK' op_fraud_chargeback_representment='NPCI_REPRESENTMENT_COMPLETED' op_re_presentment_raise='NPCI_REPRESENTMENT_COMPLETED' op_chargeback_acceptance='MERCHANT_ACCEPTED_CHARGEBACK' op_pre_arbitration_raise = 'NPCI_ACK_CHARGEBACK' op_pre_arbitration_acceptance='MERCHANT_ACCEPTED_CHARGEBACK' op_pre_arbitration_declined='NPCI_REPRESENTMENT_COMPLETED' pointer='/Adjtype'/}/}",
            "currentEvent" : "/{/{translateTxt op_chargeback_raise='NPCI_ACK_CHARGEBACK' op_fraud_chargeback_raise='NPCI_ACK_CHARGEBACK' op_fraud_chargeback_acceptance='MERCHANT_ACCEPT_CHARGEBACK' op_fraud_chargeback_representment='COMPLETE_NPCI_REPRESENTMENT' op_re_presentment_raise='COMPLETE_NPCI_REPRESENTMENT' op_chargeback_acceptance='MERCHANT_ACCEPT_CHARGEBACK' op_pre_arbitration_raise = 'NPCI_ACK_CHARGEBACK' op_pre_arbitration_acceptance='MERCHANT_ACCEPT_CHARGEBACK' op_pre_arbitration_declined='COMPLETE_NPCI_REPRESENTMENT' pointer='/Adjtype'/}/}",
        	"raisedAt" : "/{/{localDateToDateTime  pointer='/Adjdate'/}/}",
            "disputedAmount" : /{/{rupeesToPaise pointer='/Adjamount'/}/},
            "penaltyAmount" : /{/{rupeesToPaise pointer='/Compensation amount'/}/},
        	"instrumentTransactionId" : "/{/{[UPI Transaction ID]/}/}"
        }
      disputeConfig: >-
        {
            "currentDisputeStage" : "/{/{translateTxt op_chargeback_raise='FIRST_LEVEL' op_chargeback_acceptance='FIRST_LEVEL' op_fraud_chargeback_raise='FIRST_LEVEL' op_fraud_chargeback_representment='FIRST_LEVEL' op_re_presentment_raise='FIRST_LEVEL' op_pre_arbitration_declined='PRE_ARBITRATION'  op_pre_arbitration_raise = 'PRE_ARBITRATION' op_pre_arbitration_acceptance='PRE_ARBITRATION' pointer='/Adjtype'/}/}",
            "instrumentTransactionId" : "/{/{[UPI Transaction ID]/}/}",
            "transactionAmount" : /{/{rupeesToPaise pointer='/Txnamount'/}/},
            "rrn" : "/{/{replace [RRN] "'" ""/}/}",
            "disputeCategory": "/{/{translateTxt op_chargeback_raise='SERVICE_CHARGEBACK' op_chargeback_acceptance='SERVICE_CHARGEBACK' op_fraud_chargeback_raise='FRAUD_CHARGEBACK' op_fraud_chargeback_representment='FRAUD_CHARGEBACK' op_pre_arbitration_raise='SERVICE_CHARGEBACK' op_pre_arbitration_declined='SERVICE_CHARGEBACK'  op_pre_arbitration_acceptance='SERVICE_CHARGEBACK' op_re_presentment_raise='SERVICE_CHARGEBACK' pointer='/Adjtype'/}/}",
            "disputeIssuer": "PHP"
        }
      metaConfig: >-
        {
            "type": "WALLET_CHARGEBACK",
            "transactionDate": "/{/{localDateToDateTime  pointer='/Txndate'/}/}",
            "utr" : "/{/{replace [RRN] "'" ""/}/}"
        }

    disputeStageTTLDaysMap:
      FIRST_LEVEL: 30
      PRE_ARBITRATION: 60

accountingEventTypeMapping:
  UPI_CHARGEBACK:
    CHARGEBACK_RECOVERY: UPI_CHARGEBACK_RECOVERY
    CHARGEBACK_PENALTY_RECOVERY: UPI_CHARGEBACK_PENALTY_RECOVERY
    CHARGEBACK_RECOVERY_REVERSAL: UPI_CHARGEBACK_RECOVERY_REVERSAL
  PG_CHARGEBACK:
    CHARGEBACK_RECOVERY: PG_CHARGEBACK_RECOVERY
    CHARGEBACK_PENALTY_RECOVERY: PG_CHARGEBACK_PENALTY_RECOVERY
    CHARGEBACK_RECOVERY_REVERSAL: PG_CHARGEBACK_RECOVERY_REVERSAL
  EDC_CHARGEBACK:
    CHARGEBACK_RECOVERY: PG_CHARGEBACK_RECOVERY
    CHARGEBACK_PENALTY_RECOVERY: PG_CHARGEBACK_PENALTY_RECOVERY
    CHARGEBACK_RECOVERY_REVERSAL: PG_CHARGEBACK_RECOVERY_REVERSAL
  NB_CHARGEBACK:
    CHARGEBACK_RECOVERY: NB_CHARGEBACK_RECOVERY
    CHARGEBACK_RECOVERY_REVERSAL: NB_CHARGEBACK_RECOVERY_REVERSAL
  BBPS_TAT_BREACH_TOA:
    PENALTY_RECOVERY: MERCHANT_PENALTY_RECOVERY

accountingTransactionTypeMapping:
  UPI_CHARGEBACK:
    CHARGEBACK_RECOVERY: UPI_CHARGEBACK_RECOVERY
    CHARGEBACK_PENALTY_RECOVERY: UPI_CHARGEBACK_PENALTY_RECOVERY
    CHARGEBACK_RECOVERY_REVERSAL: UPI_CHARGEBACK_RECOVERY_REVERSAL
  PG_CHARGEBACK:
    CHARGEBACK_RECOVERY: PG_CHARGEBACK_RECOVERY
    CHARGEBACK_PENALTY_RECOVERY: PG_CHARGEBACK_PENALTY_RECOVERY
    CHARGEBACK_RECOVERY_REVERSAL: PG_CHARGEBACK_RECOVERY_REVERSAL
  EDC_CHARGEBACK:
    CHARGEBACK_RECOVERY: PG_CHARGEBACK_RECOVERY
    CHARGEBACK_PENALTY_RECOVERY: PG_CHARGEBACK_PENALTY_RECOVERY
    CHARGEBACK_RECOVERY_REVERSAL: PG_CHARGEBACK_RECOVERY_REVERSAL
  NB_CHARGEBACK:
    CHARGEBACK_RECOVERY: NB_CHARGEBACK_RECOVERY
    CHARGEBACK_RECOVERY_REVERSAL: NB_CHARGEBACK_RECOVERY_REVERSAL
  BBPS_TAT_BREACH_TOA:
    PENALTY_RECOVERY: MERCHANT_PENALTY_RECOVERY

disputeWorkflowStateConfig:
  disputeWorkflowEndingStates:
    PG_CHARGEBACK:
      FIRST_LEVEL:
        - PG_REPRESENTMENT_COMPLETED
        - PG_ACCEPTANCE_COMPLETED
        - CHARGEBACK_ABSORB_REVERSED
        - REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_ACCEPTED
        - DEBIT_RECEIVED

  retryableStates:
    UDIR_OUTGOING_COMPLAINT:
      FIRST_LEVEL:
        - UDIR_COMPLAINT_REJECTED
        - FAILURE

olympusIMClientConfig:
  httpConfig:
    clientId: olympus
    usingZookeeper: false
    host: olympus-im-stage.phonepe.com
    port: 443
    secure: true
    serviceName: olympusIM
    environment: stage
    connections: 10
    idleTimeOutSeconds: 30
  authConfig:
    componentId: STRATOS
    componentInstanceId: STRATOS
    componentInstanceGroupId: STRATOS_CIG
    clientId: STRATOS
    clientKey: 38cd8321-e062-47c8-9de6-13fe9e31baac
  publicEndpoint: http://localhost:10000
  authEndpoint: https://olympus-im-stage.phonepe.com
  resourcePrefix: /

disputeCategoryMapping:
  SERVICE_CHARGEBACK:
    - 4837
    - 4849
    - 4863
    - 4870
    - 4842
    - 4804
    - 4808
    - 4846
    - 4859
    - 4880
    - 4855
    - 4860
    - 4831
    - 4807
    - 4812
    - 4841
    - 4853
    - 4854
    - 4902
    - 10.5
    - 11.1
    - 11.2
    - 11.3
    - 12.1
    - 12.2
    - 12.3
    - 12.4
    - 12.5
    - 12.7
    - 13.1
    - 13.2
    - 13.3
    - 13.4
    - 13.5
    - 13.6
    - 13.7
    - 13.8
    - 13.9
    - 12.6.2
    - 1061
    - 1062
    - 1063
    - 1064
    - 1081
    - 1083
    - 1121
    - 1122
    - 1123
    - 1104
    - 1125
    - 1126
    - 1131
    - 1082
    - 1087
    - 1088
    - 1147
    - 1065
    - 1067
    - 1068
    - 1069
    - 1101
    - 1102
    - 1103
    - 1001
    - 1002
    - 1003
    - 1008
    - 1009
    - 1010
    - 1011
    - 1012
    - 1013
    - 1020
    - 1023
    - 1046
    - 28
    - 30
    - 34
    - 6305
    - 6321
    - 6322
    - 6323
    - 6342
    - 6343
  FRAUD_CHARGEBACK:
    - 4840
    - 4871
    - 10.1
    - 10.2
    - 10.3
    - 10.4
    - 1141
    - 1142
    - 1143
    - 1146
    - 1149
    - 1150
    - 1144
    - 1004
    - 1006
    - 33
    - 6341
  DUPLICATE_CHARGEBACK:
    - 4834
    - 12.6
    - 12.6.1
    - 1084

chargebackAnomalyMerchants:
  - PHONEPECARDPAYMENT
  - PHONEPEWALLETTOPUP
  - PHONEPERENTPAYMENT

pulseTypeCellMapping:
  P2PM_TOA: STRATOS_P2P_MERCHANT_DEEMED_FAILURE_V2_TEST_2
  NOTIONAL_CREDIT_TOA: STRATOS_NOTIONAL_WALLET_TOA_EVENT

p2pmToaConfig:
  firstPayStatusCheckDelayInSec: 30
  payStatusCheckIncrementalDelayInSec: 30
  postEntryInitiationFailedRetryDelayInSec: 30
  p2pmToaPerDayNotificationThreshold: 300
  payStatusCheckMaxRetry: 3
  postEntryHandlerQueueMaxRetry: 3
  p2pmToaFailedMaxRetry: 3
  breachNotificationEmailId: <EMAIL>

actorIgnoreErrorCodes:
  PG_MIS_ROW_PROCESSOR:
    - INVALID_TRANSITION
    - DISPUTE_NOT_FOUND
    - INVALID_DEBIT_AMOUNT
  FILE_ROW_PROCESSOR:
    - DISPUTE_WORKFLOW_NOT_FOUND
    - INVALID_TRANSITION
    - DUPLICATE_CHARGEBACK
    - INVALID_CREDIT_AMOUNT
    - PREVIOUS_DISPUTE_WORKFLOW_NOT_ENDED
    - FIRST_LEVEL_DISPUTE_NOT_FOUND
    - INVALID_UPI_ID
    - INVALID_DEBIT_AMOUNT
    - INVALID_TRANSACTION
    - NON_EXTERNAL_MERCHANT_TRANSACTION
  FILE_PROCESSOR:
    - DUPLICATE_FILE_NAME
  KRATOS_WORKFLOW_PROCESSOR:
    - FRA_REQUEST_CREATION_FAILED


apiFilterConfig:
  apiState: WRITE_API

toaConfig:
  NOTIONAL_CREDIT_TOA:
    firstPayStatusCheckDelayInSec: 30
    payStatusCheckIncrementalDelayInSec: 30
    postEntryInitiationFailedRetryDelayInSec: 30
    toaPerDayNotificationThreshold: ********
    toaRetryConfig:
      type: DEFAULT
      payStatusCheckMaxRetry: 3
      postEntryHandlerQueueMaxRetry: 3
      toaFailedMaxRetry: 3
    breachNotificationEmailIds: ["<EMAIL>"]
  BBPS_TAT_BREACH_TOA:
    firstPayStatusCheckDelayInSec: 30
    payStatusCheckIncrementalDelayInSec: 100
    postEntryInitiationFailedRetryDelayInSec: 30
    toaPerDayNotificationThreshold: ********
    toaRetryConfig:
      type: MULTI_INSTRUMENT
      payStatusCheckMaxRetry: 3
      postEntryHandlerQueueMaxRetry: 3
      terminalRetryDestination: EGV
      destinationWiseToaFailedMaxRetry:
        #ACCOUNT: 3
        EGV: 3
    breachNotificationEmailIds: [ "<EMAIL>", "<EMAIL>", "<EMAIL>" ]
    singleToaAmountThreshold: 0
    toaEligibilityThresholdDays: 186
    applyKSOnDayLevelAmountThresholdBreached: true

npciDisputeFlag:
  npciDisputeStageFlagMap:
    FIRST_LEVEL: B
    PRE_ARBITRATION: P
  npciDisputeCategoryFlagMap:
    FRAUD_CHARGEBACK: FC
    SERVICE_CHARGEBACK: B

disputeWorkflowStateTTLDaysMap:
  FRAUD_REPRESENTMENT_COMPLETED: 90

fraudChargebackTtl:
  WALLET_CHARGEBACK:
    disputeCategoryTTLMap:
      FRAUD_CHARGEBACK: 4

autoApprovalConfig:
  UPI_CHARGEBACK_FIRST_LEVEL:
    - currentState: RECOVER_CHARGEBACK_REQUESTED
      toEvent: APPROVE_RECOVER_CHARGEBACK

docstoreBaseUrl: https://stg-docstore.phonepe.com/public/

aerospikeConfig:
  namespace: stratos
  aerospikeBundleConfig:
    hosts:
      - host: stg-aerospikepp301.phonepe.nb6
        port: 3000
      - host: stg-aerospikepp302.phonepe.nb6
        port: 3000
      - host: stg-aerospikepp303.phonepe.nb6
      - host: localhost
        port: 3000
    retries: 3
    sleepBetweenRetries: 10
    maxConnectionsPerNode: 32
    threadPoolSize: 512
    healthcheckEnabled: true
  readPolicy:
    maxRetries: 3
    readModeAP: ONE
    replica: MASTER_PROLES
    sleepBetweenRetries: 10
    totalTimeout: 1000
    sendKey: true
  writePolicy:
    recordExistsAction: REPLACE
    maxRetries: 3
    readModeAP: ALL
    replica: MASTER_PROLES
    sleepBetweenRetries: 10
    commitLevel: COMMIT_ALL
    totalTimeout: 1000
    sendKey: true
  batchPolicy:
    maxRetries: 3
    readModeAP: ALL
    replica: MASTER_PROLES
    sleepBetweenRetries: 10
    totalTimeout: 1000
    sendKey: true
    maxConcurrentThreads: 2
    allowInline: true
    sendSetName: false
  ttlDurations:
    STATE_TRANSITION_LOCK: 2m
    ACTION_METADATA_STORE: 1h
    WORKFLOW_CONTEXT: 2h
    WORKFLOW_CONTEXT_LOCK: 5s
    OTP_TOKEN: 11m
    CLIENT_SESSION: 16m
    ACCESS_TOKEN: 15m
    UI_REQUEST_CONTEXT: 1h
    ENTITY_DETAILS: 2h
    WORKFLOW_INIT_ASYNC_FOR_REQUEST_ID: 5m
    WORKFLOW_INIT_ASYNC_FOR_WORKFLOW_ID: 5m
    PROFILE_STAGE: 1d
    PROFILE_CHANGE_LOCK: 1d
  defaultTtlDuration: 1s
stratosAerospikeTTLSeconds:
  STATE_TRANSITION_LOCK: 120
  FILE_PROCESSING: 31622400
  P2PM_TOA_AGGREGATION: 2592000 # 30 days
  FILE_UPLOAD_LOCK: 300 # 5 min
  TOA_AGGREGATION: 2592000 # 30 days
penaltyAerospikeTTLSeconds:
  PENALTY_PROBABLE: 2592000
actorsConfig:
  ABORT_WORKFLOW:
    concurrency: 1
    delayed: false
    exchange: ${USER}.ABORT_WORKFLOW
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 15
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 2s
  ABORT_WORKFLOW_V2:
    concurrency: 1
    delayed: false
    exchange: ${USER}.ABORT_WORKFLOW_V2
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      type: NO_RETRY
  ACKNOWLEDGE_SMS_CONSENT:
    concurrency: 1
    delayed: false
    exchange: ${USER}.ACKNOWLEDGE_SMS_CONSENT
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 3
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 1s
  ACTION_EXECUTOR:
    concurrency: 1
    delayed: false
    exchange: ${USER}.ACTION_EXECUTOR
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  AUTO_RETRY_ACTION:
    concurrency: 1
    delayed: false
    exchange: ${USER}.AUTO_RETRY_ACTION
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  AUTO_RETRY_ACTION_CLOCKWORK_CALLBACK:
    concurrency: 1
    delayed: false
    exchange: ${USER}.AUTO_RETRY_ACTION_CLOCKWORK_CALLBACK
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  CLIENT_WORKFLOW_CALLBACK:
    concurrency: 1
    delayed: false
    exchange: ${USER}.CLIENT_WORKFLOW_CALLBACK
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  CONFIRMATION_ACTION:
    concurrency: 1
    delayed: false
    exchange: ${USER}.CONFIRMATION_ACTION
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 3
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 1s
  DELETE_DOCSTORE_FILE:
    concurrency: 1
    delayed: false
    exchange: ${USER}.DELETE_DOCSTORE_FILE
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 1s
  DOCUMENT_MASK_ACTION:
    concurrency: 1
    delayed: false
    exchange: ${USER}.DOCUMENT_MASK_ACTION
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  DOCUMENT_PREFILL:
    concurrency: 1
    delayed: false
    exchange: ${USER}.DOCUMENT_PREFILL
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      type: NO_RETRY
  EVENT_INGESTION:
    concurrency: 1
    delayed: false
    exchange: ${USER}.EVENT_INGESTION
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  HANDLE_ACTION_COMPLETION:
    concurrency: 1
    delayed: false
    exchange: ${USER}.HANDLE_ACTION_COMPLETION
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  HANDLE_INITIAL_ACTION_WORKFLOW_STEP_COMPLETION:
    concurrency: 1
    delayed: false
    exchange: ${USER}.HANDLE_INITIAL_ACTION_WORKFLOW_STEP_COMPLETION
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  HANDLE_PSEUDO_SUCCESS_ACTION_COMPLETION:
    concurrency: 1
    delayed: false
    exchange: ${USER}.HANDLE_PSEUDO_SUCCESS_ACTION_COMPLETION
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  HANDLE_PSEUDO_SUCCESS_WORKFLOW_STEP_COMPLETION:
    concurrency: 1
    delayed: false
    exchange: ${USER}.HANDLE_PSEUDO_SUCCESS_WORKFLOW_STEP_COMPLETION
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  HANDLE_PSEUDO_SUCCESS_WORKFLOW_STEP_COMPLETION_V2:
    concurrency: 1
    delayed: false
    exchange: ${USER}.HANDLE_PSEUDO_SUCCESS_WORKFLOW_STEP_COMPLETION_V2
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  HANDLE_TTL_CALLBACK:
    concurrency: 1
    delayed: false
    exchange: ${USER}.HANDLE_TTL_CALLBACK
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  HANDLE_WORKFLOW_STEP_COMPLETION:
    concurrency: 1
    delayed: false
    exchange: ${USER}.HANDLE_WORKFLOW_STEP_COMPLETION
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  HAWKEYE_NOTIFICATION:
    concurrency: 1
    delayed: false
    exchange: ${USER}.HAWKEYE_NOTIFICATION
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  MOVE_TO_IN_PROGRESS_WORKFLOW_STEP:
    concurrency: 1
    delayed: false
    exchange: ${USER}.MOVE_TO_IN_PROGRESS_WORKFLOW_STEP
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  OCR_ACTION_V2:
    concurrency: 1
    delayed: false
    exchange: ${USER}.OCR_ACTION_V2
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  PERSIST_FORENSICS:
    concurrency: 1
    delayed: false
    exchange: ${USER}.PERSIST_FORENSICS
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 3
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 1s
  PERSIST_KEY_VALUE_PAIRS_FROM_API_CALL:
    concurrency: 1
    delayed: false
    exchange: ${USER}.PERSIST_KEY_VALUE_PAIRS_FROM_API_CALL
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 3
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 1s
  POST_COMPLETION_ACTION_CONFIG_PROCESSOR:
    concurrency: 1
    delayed: false
    exchange: ${USER}.POST_COMPLETION_ACTION_CONFIG_PROCESSOR
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 3
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 1s
  PROCESS_DOCUMENT_MASKING_DRISHTI_CALLBACK:
    concurrency: 1
    delayed: false
    exchange: ${USER}.PROCESS_DOCUMENT_MASKING_DRISHTI_CALLBACK
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  PROCESS_DRISHTI_OCR_ACTION:
    concurrency: 1
    delayed: false
    exchange: ${USER}.PROCESS_DRISHTI_OCR_ACTION
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 10
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 1s
  PROCESS_OCR_ACTION_V2_CALLBACK:
    concurrency: 1
    delayed: false
    exchange: ${USER}.PROCESS_OCR_ACTION_V2_CALLBACK
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  PROCESS_OCR_DATA_DRISHTI_CALLBACK:
    concurrency: 1
    delayed: false
    exchange: ${USER}.PROCESS_OCR_DATA_DRISHTI_CALLBACK
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 10
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 1s
  PROCESS_SEARCH_CLIENT_DATA:
    concurrency: 1
    delayed: false
    exchange: ${USER}.PROCESS_SEARCH_CLIENT_DATA
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 3
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 1s
  PROCESS_SELFIE_DOCUMENT_ID_SUBMIT:
    concurrency: 1
    delayed: false
    exchange: ${USER}.PROCESS_SELFIE_DOCUMENT_ID_SUBMIT
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 3
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 1s
  PURGE_WORKFLOW:
    concurrency: 1
    delayed: false
    exchange: ${USER}.PURGE_WORKFLOW
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 1
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 1s
  REVOLVER_CALLBACK:
    concurrency: 1
    delayed: false
    exchange: ${USER}.REVOLVER_CALLBACK
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 6
      maxTimeBetweenRetries: 30s
      multipier: 50
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
  SCHEDULED_ABORT_CALLBACK_FOR_WORKFLOW_STEP:
    concurrency: 1
    delayed: false
    exchange: ${USER}.SCHEDULED_ABORT_CALLBACK_FOR_WORKFLOW_STEP
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  SCHEDULED_WORKFLOW_ABORT_CALLBACK:
    concurrency: 1
    delayed: false
    exchange: ${USER}.SCHEDULED_WORKFLOW_ABORT_CALLBACK
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  SCHEDULE_ABORT_FOR_WORKFLOW_STEP:
    concurrency: 1
    delayed: false
    exchange: ${USER}.SCHEDULE_ABORT_FOR_WORKFLOW_STEP
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  SCHEDULE_WORKFLOW_ABORT:
    concurrency: 1
    delayed: false
    exchange: ${USER}.SCHEDULE_WORKFLOW_ABORT
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  SECTION_SUBMIT:
    concurrency: 1
    delayed: false
    exchange: ${USER}.SECTION_SUBMIT
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  SECTION_SUBMIT_V2:
    concurrency: 1
    delayed: false
    exchange: ${USER}.SECTION_SUBMIT_V2
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  SKIP_WORKFLOW_STEP:
    concurrency: 1
    delayed: false
    exchange: ${USER}.SKIP_WORKFLOW_STEP
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      type: NO_RETRY
  TEXT_COMPARISON_PROCESSOR:
    concurrency: 1
    delayed: false
    exchange: ${USER}.TEXT_COMPARISON_PROCESSOR
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  TRIGGER_OTP_HURDLE:
    concurrency: 1
    delayed: false
    exchange: ${USER}.TRIGGER_OTP_HURDLE
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  TRIGGER_OTP_HURDLE_V2:
    concurrency: 1
    delayed: false
    exchange: ${USER}.TRIGGER_OTP_HURDLE_V2
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 10
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 1s
  TRIGGER_SMS_CONSENT_HURDLE:
    concurrency: 1
    delayed: false
    exchange: ${USER}.TRIGGER_SMS_CONSENT_HURDLE
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 3
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 1s
  TRIGGER_WORKFLOW_STEP:
    concurrency: 1
    delayed: false
    exchange: ${USER}.TRIGGER_WORKFLOW_STEP
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  VERIFY_OTP_HURDLE:
    concurrency: 1
    delayed: false
    exchange: ${USER}.VERIFY_OTP_HURDLE
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 5
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  VERIFY_OTP_HURDLE_V2:
    concurrency: 1
    delayed: false
    exchange: ${USER}.VERIFY_OTP_HURDLE_V2
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 10
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 1s
  WORKFLOW_AUTO_ABORT:
    concurrency: 1
    delayed: false
    exchange: ${USER}.WORKFLOW_AUTO_ABORT
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 15
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 2s
  WORKFLOW_AUTO_SKIP:
    concurrency: 1
    delayed: false
    exchange: ${USER}.WORKFLOW_AUTO_SKIP
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      type: NO_RETRY
  WORKFLOW_INIT:
    concurrency: 1
    delayed: false
    exchange: ${USER}.WORKFLOW_INIT
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 1
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s
  WORKFLOW_INIT_V2:
    concurrency: 1
    delayed: false
    exchange: ${USER}.WORKFLOW_INIT_V2
    prefetchCount: 1
    prefix: ${USER}.kaizen
    retryConfig:
      maxAttempts: 1
      type: COUNT_LIMITED_FIXED_WAIT
      waitTime: 5s

gandalfClientConfig:
  secureCookies: false
  httpConfig:
    clientId: gandalfClient
    usingZookeeper: true
    host: api-internal.nixy.stg-drove.phonepe.nb6
    serviceName: api-internal
    rootPathPrefix: apis/gandalf
    environment: stage
    connections: 10
    idleTimeOutSeconds: 30
  authConfig:
    clientId: stratos
    clientKey: 1234
    namespace: stratos
  permissionCacheConfig:
    ttlSeconds: 900
    enabled: true

aesCipherKeyConfig:
  1: key1
  2: key2
  3: key3

httpCallbackClientConfigMap: {}

ttlConfig:
  WALLET_CHARGEBACK:
    disputeStageTTLMap:
      FIRST_LEVEL: 15
      PRE_ARBITRATION: 30

walletDisputeStageMap:
  FIRST_LEVEL: F
  PRE_ARBITRATION: P

callbackConfig:
  DISPUTE_CREATION: []
  STATE_CHANGE: []

templateCacheConfig:
  enabled: false
  namespace: core
  setName: core
  expiry: 900

caffeineCacheConfig:
  defaultCacheConfig:
    maxElements: 10000
    expiry: 1h
    refreshInterval: 30m
  cacheConfigMap:
    PROFILE_CACHE:
      maxElements: 10000
      expiry: 1d
      refreshInterval: 6h
    PROFILE_STEP_CACHE:
      maxElements: 10000
      expiry: 1d
      refreshInterval: 6h
    PROFILE_ID_PROFILE_STEPS_CACHE:
      maxElements: 10000
      expiry: 1d
      refreshInterval: 6h

olympusTenantIdMap:
  UPI_CHARGEBACK: CIG_STRATOS_CIG_UPI_CHARGEBACK
  WALLET_CHARGEBACK: CIG_STRATOS_CIG_WALLET_CHARGEBACK

fileUploadChecker: []

ignoreSignalStateConfig:
  PG_CHARGEBACK:
    V1:
      - RECOVER_CHARGEBACK_EVENT_ACCEPTED
      - DEBIT_RECEIVED
      - REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_ACCEPTED
  EDC_CHARGEBACK:
    V1:
      - RECOVER_CHARGEBACK_EVENT_ACCEPTED
      - DEBIT_RECEIVED
      - REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_ACCEPTED