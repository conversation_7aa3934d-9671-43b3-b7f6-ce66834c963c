package com.phonepe.merchant.platform.stratos.server.core.services.impls;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.DisputeStatus;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.EvidenceFilterParams;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.EvidenceIdentifierParams;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.EvidenceUploadPayload;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.DisputeDetails;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.EvidenceDetail;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.EvidenceResponse;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.clients.DocstoreClient;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs;
import com.phonepe.merchant.platform.stratos.server.core.helpers.id.IdHelper;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStateMapper;
import com.phonepe.merchant.platform.stratos.server.core.models.EvidenceStatus;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.EvidenceService;
import com.phonepe.merchant.platform.stratos.server.core.services.KaizenService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.TransformationUtils;
import com.phonepe.platform.docstore.model.response.DocStoreUploadResponse;
import com.phonepe.stratos.kaizen.models.data.ActionStatus;
import com.phonepe.stratos.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadWithMetaDataActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadActionMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Singleton
public class EvidenceServiceImpl implements EvidenceService {
    private final DocstoreClient docstoreClient;
    private final DisputeService disputeService;
    private final DisputeWorkflowRepository disputeWorkflowRepository;
    private final String docstoreBaseUrl;
    private final KaizenService kaizenService;

    @Inject
    public EvidenceServiceImpl(
            DocstoreClient docstoreClient,
            DisputeService disputeService,
            DisputeWorkflowRepository disputeWorkflowRepository,
            @Configs.DocstoreBaseUrl
            String docstoreBaseUrl,
            KaizenService kaizenService) {
        this.docstoreClient = docstoreClient;
        this.disputeService = disputeService;
        this.disputeWorkflowRepository = disputeWorkflowRepository;
        this.docstoreBaseUrl = docstoreBaseUrl;
        this.kaizenService = kaizenService;
    }


    @Override
    public EvidenceDetail upload(EvidenceUploadPayload payload, InputStream inputStream, FormDataContentDisposition fileMetaData) {
        try {
            DisputeDetails dispute = disputeService.validateAndGetDispute(payload.getDisputeId(),
                    payload.getMerchantId(),DisputeStatus.NEEDS_ACTION);

            String disputeWorkflowId = dispute.getDisputeTimeline().get(dispute.getDisputeTimeline().size()-1)
                    .getDisputeWorkflowId();

            byte[] fileBytes = IOUtils.toByteArray(inputStream);

            DocStoreUploadResponse docStoreUploadResponse =
                    docstoreClient.pushFilePublic(fileBytes, fileMetaData.getFileName());

            if (!docStoreUploadResponse.isSuccess()) {
                log.error("Failed to upload file to docstore. Response : {}",docStoreUploadResponse);
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.INTERNAL_SERVER_ERROR, Collections.emptyMap());
            }
            StoredDocumentUploadWithMetaDataActionMetadata evidence = (StoredDocumentUploadWithMetaDataActionMetadata)kaizenService.uploadEvidence(
                    disputeWorkflowId,
                    docStoreUploadResponse.getContext().getId(),
                    payload.getMetadata())
                    .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.DB_ERROR,
                    Collections.emptyMap()));
            return TransformationUtils.toEvidenceDetail(evidence,disputeWorkflowId,docstoreBaseUrl);
        }
        catch (Exception e){
            log.error("Error in uploading file", e);
            throw DisputeExceptionUtil.propagate(e);
        }
    }

    @Override
    public EvidenceResponse listEvidences(EvidenceFilterParams params) {
        try{
            List<DisputeWorkflow> dwList = disputeWorkflowRepository.selectByDisputeId(params.getDisputeId());
            dwList = dwList.stream().filter(d -> d.getDispute().getMerchantId().equals(params.getMerchantId()))
                    .collect(Collectors.toList());
            if(dwList.isEmpty())
                throw  DisputeExceptionUtil.error(StratosErrorCodeKey.DISPUTE_NOT_FOUND,
                        Map.of("Dispute not found", params.getDisputeId()));

            if(params.getDisputeWorkflowIds()!= null && !params.getDisputeWorkflowIds().isEmpty()){
                dwList = dwList.stream().filter(d -> params.getDisputeWorkflowIds().contains(d.getDisputeWorkflowId()))
                        .collect(Collectors.toList());
                if(dwList.isEmpty()){
                    throw  DisputeExceptionUtil.error(StratosErrorCodeKey.DISPUTE_WORKFLOW_NOT_FOUND,
                        Collections.emptyMap());
                }
            }
            List<EvidenceDetail> toReturn = new ArrayList<>();

            for (DisputeWorkflow dw : dwList) {
                toReturn.addAll(kaizenService.getAllEvidences(dw.getCommunicationId()).stream().map(
                        evidence -> TransformationUtils.toEvidenceDetail(evidence,dw.getDisputeWorkflowId(),docstoreBaseUrl)
                ).collect(Collectors.toSet()));
            }

            if(params.getState()!=null)
                toReturn = toReturn.stream().filter( evidenceDetail -> evidenceDetail.getStatus().equals(params.getState())).toList();

            return EvidenceResponse.builder()
                .evidenceList(toReturn)
                .build();


        } catch (Exception e) {
            log.error("Exception while fetching evidences",e);
            throw DisputeExceptionUtil.propagate(e);
        }
    }

    @Override
    public EvidenceDetail delete(EvidenceIdentifierParams params) {
        try{
            List<DisputeWorkflow> dwList = disputeWorkflowRepository.selectByDisputeId(params.getDisputeId());
            if(dwList.isEmpty())
                throw  DisputeExceptionUtil.error(StratosErrorCodeKey.DISPUTE_NOT_FOUND,
                    Map.of("Dispute not found", params.getDisputeId()));

            dwList = dwList.stream().filter(d -> d.getDispute().getMerchantId().equals(params.getMerchantId()))
                    .collect(Collectors.toList());
            if(dwList.isEmpty())
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_MERCHANT_ID,
                    Map.of(Constants.MESSAGE, "Merchant ID does not match the passed auth key."));

            dwList = dwList.stream().filter(d -> d.getCurrentState().accept(new DisputeStateMapper(),d).equals(DisputeStatus.NEEDS_ACTION))
                    .collect(Collectors.toList());
            if(dwList.isEmpty())
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.TRANSITION_NOT_ALLOWED, new HashMap<>());
            //only 1 dw can be in needs action state
            DisputeWorkflow dw = dwList.get(0);
            List<StoredDocumentUploadWithMetaDataActionMetadata> evidences = kaizenService.getAllEvidences(dw.getCommunicationId());
            if(evidences.stream().noneMatch(e -> e.getActionId().equals(params.getEvidenceId()) && e.getStatus().equals(ActionStatus.ACTIVE)))
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.EVIDENCE_NOT_FOUND,
                        Map.of("Evidence not found", params.getEvidenceId()));
            StoredActionMetadata evidence = kaizenService.deleteEvidence(params.getEvidenceId()).orElseThrow(
                    () -> DisputeExceptionUtil.error(StratosErrorCodeKey.EVIDENCE_NOT_FOUND,
                    Map.of("Evidence not found", params.getEvidenceId())));

            return TransformationUtils.toEvidenceDetail((StoredDocumentUploadWithMetaDataActionMetadata) evidence,
                    dw.getDisputeWorkflowId(),docstoreBaseUrl);
        } catch (Exception e) {
            log.error("Exception while deleting evidence", e);
            throw DisputeExceptionUtil.propagate(e);
        }
    }
}
