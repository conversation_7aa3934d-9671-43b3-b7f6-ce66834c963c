package com.phonepe.merchant.platform.stratos.server.core.utils;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import java.util.Map;
import lombok.experimental.UtilityClass;

@UtilityClass
public class MapperUtils {

    private ObjectMapper mapper;

    public void init(final ObjectMapper objectMapper) {
        mapper = setProperties(objectMapper);
    }


    public JsonNode convertToJsonNode(Map<String,Object> contextMap) {
        try {
            return mapper.valueToTree(contextMap);
        } catch (final Exception e) {
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DESERIALIZATION_ERROR, e);
        }
    }

    public <T> T deserialize(final JsonNode data, final Class<T> valueType) {
        try {
            return mapper.treeToValue(data, valueType);
        } catch (final Exception e) {
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DESERIALIZATION_ERROR, e);
        }
    }

    public <T> T deserialize(final byte[] data, final Class<T> valueType) {
        try {
            return mapper.readValue(data, valueType);
        } catch (final Exception e) {
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DESERIALIZATION_ERROR, e);
        }
    }

    public <T> T deserialize(final String data, final Class<T> valueType) {
        try {
            return String.class.equals(valueType) ? valueType.cast(data)
                : mapper.readValue(data, valueType);
        } catch (final Exception e) {
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DESERIALIZATION_ERROR, e);
        }
    }

    public <T> T deserialize(final byte[] data, final TypeReference<T> typeReference) {
        try {
            return mapper.readValue(data, typeReference);
        } catch (final Exception e) {
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DESERIALIZATION_ERROR, e);
        }
    }

    public <T> T deserialize(final String data, final TypeReference<T> typeReference) {
        try {
            return mapper.readValue(data, typeReference);
        } catch (final Exception e) {
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DESERIALIZATION_ERROR, e);
        }
    }

    public String serializeToString(final Object data) {
        try {
            return mapper.writeValueAsString(data);
        } catch (final JsonProcessingException e) {
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.SERIALIZATION_ERROR, e);
        }
    }

    public byte[] serializeToBytes(final Object data) {
        try {
            return mapper.writeValueAsBytes(data);
        } catch (final JsonProcessingException e) {
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.SERIALIZATION_ERROR, e);
        }
    }

    public <T> T convertValue(final Object fromValue, final TypeReference<T> typeReference) {
        try {
            return mapper.convertValue(fromValue, typeReference);
        } catch (final Exception e) {
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DESERIALIZATION_ERROR, e);
        }
    }

    public JsonNode convertToJsonNode(Object javaObject) {
        try {
            return mapper.valueToTree(javaObject);
        } catch (final Exception e) {
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DESERIALIZATION_ERROR, e);
        }
    }
    public JsonNode convertToJsonNode(String inputString) {
        try {
            return mapper.readTree(inputString);
        } catch (final Exception e) {
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DESERIALIZATION_ERROR, e);
        }
    }

    public ObjectMapper setProperties(final ObjectMapper objectMapper) {
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.setVisibility(PropertyAccessor.FIELD, JsonAutoDetect.Visibility.ANY);
        objectMapper.enable(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES);
        objectMapper.registerModule(new ParameterNamesModule(JsonCreator.Mode.PROPERTIES));
        return objectMapper;
    }
}
