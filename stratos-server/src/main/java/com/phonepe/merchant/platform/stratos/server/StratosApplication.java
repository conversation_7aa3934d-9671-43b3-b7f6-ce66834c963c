package com.phonepe.merchant.platform.stratos.server;

import com.google.inject.Key;
import com.google.inject.TypeLiteral;
import com.hystrix.configurator.core.HystrixConfigurationFactory;
import com.phonepe.central.dms.server.interceptor.DMSRequestFilter;
import com.phonepe.central.stratos.penalty.server.util.PenaltyEvaluatorUtil;
import com.phonepe.central.stratos.penalty.server.util.PenaltyValidationUtil;
import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.filter.ApiFilterEvaluator;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.olympus.im.client.OlympusIMGuiceInjectionContext;
import com.phonepe.platform.filters.ApiKiller;
import com.phonepe.platform.filters.FilterEvaluator;
import com.phonepe.platform.http.server.metrics.bundle.MetricBundle;
import com.phonepe.rosey.dwconfig.RoseyConfigSourceProvider;
import com.phonepe.services.warden.models.init.WardenModelsInit;
import io.appform.functionmetrics.FunctionMetricsManager;
import io.dropwizard.configuration.EnvironmentVariableSubstitutor;
import io.dropwizard.configuration.SubstitutingSourceProvider;
import io.dropwizard.forms.MultiPartBundle;
import io.dropwizard.setup.Bootstrap;
import io.dropwizard.setup.Environment;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.glassfish.jersey.logging.LoggingFeature;

public class StratosApplication extends BaseApplication<StratosConfiguration> {

    public static final String APP_NAME = "stratos";
    static final String ROSEY_TEAM_NAME = "mcp";

    public static void main(final String[] args) throws Exception {
        new StratosApplication().run(args);
    }

    @Override
    public String getName() {
        return APP_NAME;
    }

    @Override
    public void run(final StratosConfiguration configuration, final Environment environment)
        throws Exception {

        HystrixConfigurationFactory.init(configuration.getHystrixConfig());
        FunctionMetricsManager.initialize("commands", environment.metrics());
        MapperUtils.init(environment.getObjectMapper());
        PenaltyEvaluatorUtil.init(environment.getObjectMapper());
        final var injector = guiceBundle.getInjector();
        final var neuronBundle = getNeuronBundle(injector);
        neuronBundle.run(configuration, environment);

        if (configuration.isLoggingEnabled()) {
            environment.jersey().register(new LoggingFeature(
                Logger.getLogger(getClass().getName()), Level.INFO,
                LoggingFeature.Verbosity.PAYLOAD_ANY, null

            ));
        }
        ResourceErrorService<StratosErrorCodeKey> resourceErrorService = injector.getInstance(Key.get(new TypeLiteral<ResourceErrorService<StratosErrorCodeKey>>() {}));
        DisputeExceptionUtil.init(resourceErrorService);
        PenaltyValidationUtil.init(injector);
        FilterEvaluator filterEvaluator = new ApiFilterEvaluator(configuration.getApiFilterConfig());
        environment.jersey().register(new ApiKiller(filterEvaluator));
        environment.jersey().register(new DMSRequestFilter());

    }

    @Override
    public void initialize(final Bootstrap<StratosConfiguration> bootstrap) {

        final var localConfig = Boolean.parseBoolean(System.getProperty("localConfig", "false"));
        if (localConfig) {
            bootstrap.setConfigurationSourceProvider(
                new SubstitutingSourceProvider(
                    bootstrap.getConfigurationSourceProvider(),
                    new EnvironmentVariableSubstitutor()));
        } else {
            bootstrap.setConfigurationSourceProvider(
                new SubstitutingSourceProvider(
                    new RoseyConfigSourceProvider(ROSEY_TEAM_NAME, APP_NAME),
                    new EnvironmentVariableSubstitutor()));
        }

        final var serviceDiscoveryBundle = getServiceDiscoveryBundle();
        final var httpDiscoveryBundle = getHttpDiscoveryBundle(serviceDiscoveryBundle);
        final var configProviderBundle = appConfigProviderBundle();
        final var dbShardingBundle = dbShardingBundle();
        final var rabbitmqActorBundle = rabbitmqActorBundle(bootstrap);
        final var aerospikeBundle = aerospikeBundle(bootstrap);
        final var olympusIMBundle = olympusIMBundle(serviceDiscoveryBundle);
        final var feedTstoreClientBundle = tstoreClientBundle(olympusIMBundle, httpDiscoveryBundle);
        final var kairosBundle = kairosBundle(aerospikeBundle);
        guiceBundle = guiceBundle(rabbitmqActorBundle, serviceDiscoveryBundle, configProviderBundle, dbShardingBundle,
                aerospikeBundle, feedTstoreClientBundle, bootstrap, olympusIMBundle, httpDiscoveryBundle);

        OlympusIMGuiceInjectionContext.OLYMPUS_IM_GUICE_INJECTION_CONTEXT.setAlreadyCreatedInjector(
                guiceBundle::getInjector);
        final WardenModelsInit wardenModelsInit = new WardenModelsInit();
        wardenModelsInit.init();

        bootstrap.addBundle(serviceDiscoveryBundle);
        bootstrap.addBundle(httpDiscoveryBundle);
        bootstrap.addBundle(oorBundle());
        bootstrap.addBundle(swaggerBundle());
        bootstrap.addBundle(hystrixBundle());
        bootstrap.addBundle(aerospikeBundle);
        bootstrap.addBundle(dbShardingBundle);
        bootstrap.addBundle(validationBundle());
        bootstrap.addBundle(rabbitmqActorBundle);
        bootstrap.addBundle(new MetricBundle<>());
        bootstrap.addBundle(configProviderBundle);
        bootstrap.addBundle(new MultiPartBundle());
        bootstrap.addBundle(feedTstoreClientBundle);
        bootstrap.addBundle(getRequestInfoBundle());
        bootstrap.addBundle(olympusIMBundle);
        bootstrap.addBundle(guiceBundle);
        bootstrap.addBundle(metricIngestionBundle(olympusIMBundle, httpDiscoveryBundle));
        bootstrap.addBundle(gandalfBundle(httpDiscoveryBundle));
        bootstrap.addBundle(kairosBundle);
    }
}