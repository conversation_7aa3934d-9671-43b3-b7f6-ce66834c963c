package com.phonepe.central.dms.server.guice;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.phonepe.central.dispute.request.disputeinstance.DisputeRegisterRequest;
import com.phonepe.central.dispute.request.disputeinstance.signal.credit.CreditSignalRequest;
import com.phonepe.central.dispute.request.disputeinstance.signal.debit.DebitSignalRequest;
import com.phonepe.central.dispute.request.tenant.TenantCreateRequest;
import com.phonepe.central.dms.server.mariadb.repository.DisputeClassInstanceMetaRepository;
import com.phonepe.central.dms.server.mariadb.repository.DisputeClassInstanceRepository;
import com.phonepe.central.dms.server.mariadb.repository.DisputeRepository;
import com.phonepe.central.dms.server.mariadb.repository.TenantRepository;
import com.phonepe.central.dms.server.validation.dispute_instance.create.ActiveWorkflowDisputeInstanceValidation;
import com.phonepe.central.dms.server.validation.dispute_instance.create.DisputeInstanceAlreadyExistValidation;
import com.phonepe.central.dms.server.validation.dispute_instance.create.DisputeInstanceCreationRequestValidation;
import com.phonepe.central.dms.server.validation.dispute_instance.create.PaymentTransactionDisputeInstanceValidation;
import com.phonepe.central.dms.server.validation.signal.DisputeExistenceValidation;
import com.phonepe.central.dms.server.validation.signal.DuplicateSignalValidation;
import com.phonepe.central.dms.server.validation.signal.SignalValidation;
import com.phonepe.central.dms.server.validation.tenant.create.TenantCreationValidation;
import com.phonepe.central.dms.server.validation.tenant.create.impl.ParentForTenantExistValidation;
import com.phonepe.central.dms.server.validation.tenant.create.impl.TenantAlreadyExistValidation;
import com.phonepe.central.dispute.context.DisputeClassInstanceMetaKey;
import com.phonepe.central.workflow.server.service.WorkflowClassService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
public class ValidationChainModule extends AbstractModule {

    @Provides
    @Singleton
    public DisputeInstanceCreationRequestValidation<DisputeRegisterRequest> disputeInstanceCreationRequestValidation(final DisputeRepository disputeRepository, final DisputeClassInstanceRepository disputeClassInstanceRepository, final WorkflowClassService workflowClassService) {
        return new DisputeInstanceAlreadyExistValidation(disputeRepository, disputeClassInstanceRepository,
                new ActiveWorkflowDisputeInstanceValidation(workflowClassService, new PaymentTransactionDisputeInstanceValidation(null)));
    }

    @Provides
    @Singleton
    public TenantCreationValidation<TenantCreateRequest> tenantCreateRequestTenantCreationValidation(final TenantRepository tenantRepository) {
        return new TenantAlreadyExistValidation(tenantRepository, new ParentForTenantExistValidation(tenantRepository, null));
    }

    @Provides
    @Singleton
    public SignalValidation<CreditSignalRequest> creditSignalValidation(
            final DisputeRepository disputeRepository,
            final DisputeClassInstanceRepository disputeClassInstanceRepository,
            final DisputeClassInstanceMetaRepository disputeClassInstanceMetaRepository) {

        return new DisputeExistenceValidation<>(disputeRepository,
                new DuplicateSignalValidation<>(disputeRepository,
                        disputeClassInstanceRepository,
                        disputeClassInstanceMetaRepository,
                        DisputeClassInstanceMetaKey.CREDIT_SIGNAL,
                        null));
    }

    @Provides
    @Singleton
    public SignalValidation<DebitSignalRequest> debitSignalValidation(
            final DisputeRepository disputeRepository,
            final DisputeClassInstanceRepository disputeClassInstanceRepository,
            final DisputeClassInstanceMetaRepository disputeClassInstanceMetaRepository) {

        return new DisputeExistenceValidation<>(disputeRepository,
                new DuplicateSignalValidation<>(disputeRepository,
                        disputeClassInstanceRepository,
                        disputeClassInstanceMetaRepository,
                        DisputeClassInstanceMetaKey.DEBIT_SIGNAL,
                        null));
    }

}
