package com.phonepe.central.dms.server.mariadb.entities;

import com.phonepe.central.dispute.recovery.DisputeRecoveryMode;
import com.phonepe.central.dispute.recovery.DisputeRecoveryState;
import com.phonepe.central.dispute.user.UserType;
import com.phonepe.services.ppo.core.storage.mariadb.Sharded;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "dms_dispute_recovery", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"workflow_class_id", "dispute_class_instance_id",
                "recovery_transaction_id"})}, indexes = {
        @Index(name = "idx_dispute_class", columnList = "workflow_class_id,recovery_id")})
@FieldNameConstants
public class DisputeRecoveryEntity implements Sharded {

    private static final long serialVersionUID = -6548024317040241952L;

    @Id
    @Column(name = "id", unique = true, insertable = false, updatable = false, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;


    @NotBlank
    @Column(name = "workflow_class_id", nullable = false)
    private String workflowClassId;

    @NotBlank
    @Column(name = "dispute_class_instance_id", nullable = false)
    private String disputeClassInstanceId;


    @NotBlank
    @Column(name = "recovery_id", nullable = false)
    private String recoveryId;

    @NotBlank
    @Column(name = "recovery_transaction_id", nullable = false)
    private String recoveryTransactionId;

    @NotNull
    @Column(name = "expected_recovery_amount", nullable = false)
    private Long expectedRecoveryAmount;


    @NotNull
    @Column(name = "actual_recovery_amount", nullable = false)
    private Long actualRecoveryAmount;

    @NotBlank
    @Column(name = "tenant_id", nullable = false)
    private String tenantId;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "state", nullable = false)
    private DisputeRecoveryState state;


    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "recovery_mode", nullable = false)
    private DisputeRecoveryMode recoveryMode;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "recovered_from_user_type", nullable = false)
    private UserType recoveredFromUserType;

    @NotBlank
    @Column(name = "recovered_from_user_id", updatable = false, nullable = false)
    private String recoveredFromUserId;

    @Column(name = "partition_id", nullable = false, updatable = false)
    private int partitionId;


    @CreationTimestamp
    @Column(name = "created_at", columnDefinition = "datetime(3) DEFAULT current_timestamp", updatable = false, nullable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", columnDefinition = "datetime(3) DEFAULT current_timestamp ON UPDATE current_timestamp", nullable = false)
    private LocalDateTime updatedAt;


    @Override
    public String getKey() {
        return workflowClassId;
    }


}
