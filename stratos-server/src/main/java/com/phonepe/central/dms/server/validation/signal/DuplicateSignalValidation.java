package com.phonepe.central.dms.server.validation.signal;

import com.fasterxml.jackson.core.type.TypeReference;
import com.phonepe.central.dispute.context.DisputeClassInstanceMetaKey;
import com.phonepe.central.dispute.signal.SignalMetadata;
import com.phonepe.central.dispute.signal.SignalStatus;
import com.phonepe.central.dms.server.mariadb.entities.DisputeClassInstanceEntity;
import com.phonepe.central.dms.server.mariadb.entities.DisputeClassInstanceMetaEntity;
import com.phonepe.central.dms.server.mariadb.entities.DisputeEntity;
import com.phonepe.central.dms.server.mariadb.repository.DisputeClassInstanceMetaRepository;
import com.phonepe.central.dms.server.mariadb.repository.DisputeClassInstanceRepository;
import com.phonepe.central.dms.server.mariadb.repository.DisputeRepository;
import com.phonepe.central.workflow.request.dms.DisputeInstanceSearchRequest;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Optional;

@Slf4j
public class DuplicateSignalValidation<T extends SignalRequest> extends SignalValidation<T> {

    private final DisputeRepository disputeRepository;
    private final DisputeClassInstanceRepository disputeClassInstanceRepository;
    private final DisputeClassInstanceMetaRepository disputeClassInstanceMetaRepository;
    private final DisputeClassInstanceMetaKey metaKey;

    public DuplicateSignalValidation(DisputeRepository disputeRepository,
                                   DisputeClassInstanceRepository disputeClassInstanceRepository,
                                   DisputeClassInstanceMetaRepository disputeClassInstanceMetaRepository,
                                   DisputeClassInstanceMetaKey metaKey,
                                   SignalValidation<T> next) {
        super(next);
        this.disputeRepository = disputeRepository;
        this.disputeClassInstanceRepository = disputeClassInstanceRepository;
        this.disputeClassInstanceMetaRepository = disputeClassInstanceMetaRepository;
        this.metaKey = metaKey;
    }

    @Override
    public boolean isSignalValid(T input) {
        log.info("Validating duplicate signal for transaction ID: {}", input.getTransactionId());
        
        List<DisputeEntity> disputeEntities = disputeRepository.fetchByTransactionId(input.getTransactionId());
        if (CollectionUtils.isEmpty(disputeEntities)) {
            return true;
        }

        String disputeId = disputeEntities.get(0).getDisputeId();
        
        List<DisputeClassInstanceEntity> disputeInstances = disputeClassInstanceRepository.search(
            DisputeInstanceSearchRequest.builder().build(), disputeId);
        
        if (CollectionUtils.isEmpty(disputeInstances)) {
            return true;
        }

        DisputeClassInstanceEntity disputeInstance = disputeInstances.get(0);
        
        Optional<DisputeClassInstanceMetaEntity> existingSignal = disputeClassInstanceMetaRepository.findByMetaKey(
            disputeInstance.getWorkflowClassId(), 
            disputeInstance.getDisputeClassInstanceId(), 
            metaKey);

        if (existingSignal.isPresent()) {
            try {
                List<SignalMetadata> existingSignals = MapperUtils.deserialize(
                    existingSignal.get().getMetaValue(), 
                    new TypeReference<List<SignalMetadata>>() {});
                
                boolean signalExists = existingSignals.stream()
                    .anyMatch(signal -> signal.getTransactionId().equals(input.getTransactionId()));
                
                if (signalExists) {
                    SignalMetadata existingSignalData = existingSignals.stream()
                        .filter(signal -> signal.getTransactionId().equals(input.getTransactionId()))
                        .findFirst()
                        .orElse(null);
                    
                    if (existingSignalData != null && existingSignalData.getStatus() == SignalStatus.PROCESSED) {
                        log.error("Signal already processed for transaction ID: {}", input.getTransactionId());
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.DUPLICATE_REQUEST, 
                            "Signal already processed for transaction ID: " + input.getTransactionId());
                    }
                    
                    log.info("Signal exists but unprocessed for transaction ID: {}, will overwrite", input.getTransactionId());
                }
            } catch (Exception e) {
                log.error("Error parsing existing signal metadata for transaction ID: {}", input.getTransactionId(), e);
                throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.INTERNAL_SERVER_ERROR, e);
            }
        }
        
        return true;
    }
}
