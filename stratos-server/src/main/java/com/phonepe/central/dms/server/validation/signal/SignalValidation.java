package com.phonepe.central.dms.server.validation.signal;

import com.phonepe.central.workflow.server.validation.Validation;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public abstract class SignalValidation<T> implements Validation<T> {

    private SignalValidation<T> next;

    @Override
    public boolean validate(T input) {
        if (next == null) {
            return this.isSignalValid(input);
        }
        return this.isSignalValid(input) && next.validate(input);
    }

    public abstract boolean isSignalValid(T input);
}
