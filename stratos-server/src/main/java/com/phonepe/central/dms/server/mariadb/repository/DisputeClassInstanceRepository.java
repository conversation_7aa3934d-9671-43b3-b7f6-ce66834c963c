package com.phonepe.central.dms.server.mariadb.repository;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.central.dms.server.mariadb.entities.DisputeClassInstanceEntity;
import com.phonepe.central.workflow.request.dms.DisputeInstanceSearchRequest;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.RelationalDaoCrudRepository;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import io.appform.dropwizard.sharding.dao.RelationalDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Singleton
public class DisputeClassInstanceRepository extends RelationalDaoCrudRepository<DisputeClassInstanceEntity> {

    private static final Integer MAX_PAGE_SIZE = 2000;


    @Inject
    public DisputeClassInstanceRepository(RelationalDao<DisputeClassInstanceEntity> relationalDao) {
        super(relationalDao);
    }

    public List<DisputeClassInstanceEntity> search(final DisputeInstanceSearchRequest instanceSearchRequest, final String disputeId) {
        try {
            final var detachedCriteria = DetachedCriteria.forClass(DisputeClassInstanceEntity.class);
            if (StringUtils.isNotBlank(instanceSearchRequest.getDisputeClassInstanceId())) {
                detachedCriteria.add(Restrictions.eq(DisputeClassInstanceEntity.Fields.disputeClassInstanceId, instanceSearchRequest.getDisputeClassInstanceId()));
            }
            if (StringUtils.isNotBlank(disputeId)) {
                detachedCriteria.add(Restrictions.eq(DisputeClassInstanceEntity.Fields.disputeId, disputeId));
            }
            if (StringUtils.isNotBlank(instanceSearchRequest.getWorkflowClassId())) {
                detachedCriteria.add(
                        Restrictions.eq(DisputeClassInstanceEntity.Fields.workflowClassId, instanceSearchRequest.getWorkflowClassId()));
                return this.relationalDao.select(instanceSearchRequest.getWorkflowClassId(), detachedCriteria, 0, MAX_PAGE_SIZE);
            } else {
                return this.relationalDao.scatterGather(detachedCriteria, 0, MAX_PAGE_SIZE);
            }
        } catch (Exception exception) {
            log.error("Exception in getting dispute instance list for search request {} : ", instanceSearchRequest, exception);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DB_ERROR, exception);
        }
    }


    public void updateWorkflowInstanceId(@NotBlank String workflowClassId, @NotBlank String disputeId, @NotBlank String workflowClassInstanceId) {
        try {
            final var detachedCriteria = DetachedCriteria.forClass(DisputeClassInstanceEntity.class)
                    .add(Restrictions.eq(DisputeClassInstanceEntity.Fields.workflowClassId, workflowClassId))
                    .add(Restrictions.eq(DisputeClassInstanceEntity.Fields.disputeId, disputeId));
            relationalDao.update(workflowClassId, detachedCriteria, entity -> {
                entity.setWorkflowClassInstanceId(workflowClassInstanceId);
                return entity;
            });
        } catch (Exception exception) {
            log.error("Exception in updating the workflowinstanceID dispute instance details {} {} {}", workflowClassId, disputeId, workflowClassInstanceId, exception);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DB_ERROR, exception);
        }
    }
}
