package com.phonepe.central.dms.server.validation.signal;

import com.phonepe.central.dms.server.mariadb.entities.DisputeEntity;
import com.phonepe.central.dms.server.mariadb.repository.DisputeRepository;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Slf4j
public class DisputeExistenceValidation<T extends SignalRequest> extends SignalValidation<T> {

    private final DisputeRepository disputeRepository;

    public DisputeExistenceValidation(DisputeRepository disputeRepository, SignalValidation<T> next) {
        super(next);
        this.disputeRepository = disputeRepository;
    }

    @Override
    public boolean isSignalValid(T input) {
        log.info("Validating dispute existence for transaction ID: {}", input.getTransactionId());
        
        List<DisputeEntity> disputeEntities = disputeRepository.fetchByTransactionId(input.getTransactionId());
        
        if (CollectionUtils.isEmpty(disputeEntities)) {
            log.error("No dispute found for transaction ID: {}", input.getTransactionId());
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.DISPUTE_NOT_FOUND, 
                "No dispute exists for transaction ID: " + input.getTransactionId());
        }
        
        log.info("Dispute found for transaction ID: {}", input.getTransactionId());
        return true;
    }
}
