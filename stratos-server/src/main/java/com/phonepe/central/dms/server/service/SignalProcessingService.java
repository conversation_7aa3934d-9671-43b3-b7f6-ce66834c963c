package com.phonepe.central.dms.server.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.central.dispute.context.DisputeClassInstanceMetaKey;
import com.phonepe.central.dispute.request.disputeinstance.signal.credit.CreditSignalRequest;
import com.phonepe.central.dispute.request.disputeinstance.signal.debit.DebitSignalRequest;
import com.phonepe.central.dispute.response.signal.CreditSignalResponse;
import com.phonepe.central.dispute.response.signal.DebitSignalResponse;
import com.phonepe.central.dispute.signal.CreditSignalMetadata;
import com.phonepe.central.dispute.signal.DebitSignalMetadata;
import com.phonepe.central.dispute.signal.SignalMetadata;
import com.phonepe.central.dispute.signal.SignalStatus;
import com.phonepe.central.dms.server.mariadb.entities.DisputeClassInstanceEntity;
import com.phonepe.central.dms.server.mariadb.entities.DisputeClassInstanceMetaEntity;
import com.phonepe.central.dms.server.mariadb.entities.DisputeEntity;
import com.phonepe.central.dms.server.mariadb.repository.DisputeClassInstanceMetaRepository;
import com.phonepe.central.dms.server.mariadb.repository.DisputeClassInstanceRepository;
import com.phonepe.central.dms.server.mariadb.repository.DisputeRepository;
import com.phonepe.central.dms.server.validation.signal.SignalValidation;
import com.phonepe.central.stratos.penalty.server.util.PenaltyDBUtils;
import com.phonepe.central.workflow.request.dms.DisputeInstanceSearchRequest;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class SignalProcessingService {

    private final DisputeRepository disputeRepository;
    private final DisputeClassInstanceRepository disputeClassInstanceRepository;
    private final DisputeClassInstanceMetaRepository disputeClassInstanceMetaRepository;
    private final SignalValidation<CreditSignalRequest> creditSignalValidation;
    private final SignalValidation<DebitSignalRequest> debitSignalValidation;

    public CreditSignalResponse processCreditSignal(@NotNull CreditSignalRequest creditSignalRequest) {
        log.info("Processing credit signal for transaction ID: {}", creditSignalRequest.getTransactionId());
        
        creditSignalValidation.validate(creditSignalRequest);
        
        String payload = MapperUtils.serializeToString(creditSignalRequest);
        CreditSignalMetadata signalMetadata = new CreditSignalMetadata(
            creditSignalRequest.getTransactionId(),
            SignalStatus.UNPROCESSED,
            payload,
            creditSignalRequest.getSignalType()
        );
        
        String disputeClassInstanceId = storeSignal(creditSignalRequest.getTransactionId(), 
                                                   signalMetadata, 
                                                   DisputeClassInstanceMetaKey.CREDIT_SIGNAL);
        
        log.info("Credit signal processed successfully for transaction ID: {}", creditSignalRequest.getTransactionId());
        
        return CreditSignalResponse.builder()
            .transactionId(creditSignalRequest.getTransactionId())
            .signalType(creditSignalRequest.getSignalType())
            .disputeWorkflowInstanceId(disputeClassInstanceId)
            .message("Credit signal processed successfully")
            .build();
    }

    public DebitSignalResponse processDebitSignal(@NotNull DebitSignalRequest debitSignalRequest) {
        log.info("Processing debit signal for transaction ID: {}", debitSignalRequest.getTransactionId());
        
        debitSignalValidation.validate(debitSignalRequest);
        
        String payload = MapperUtils.serializeToString(debitSignalRequest);
        DebitSignalMetadata signalMetadata = new DebitSignalMetadata(
            debitSignalRequest.getTransactionId(),
            SignalStatus.UNPROCESSED,
            payload,
            debitSignalRequest.getSignalType()
        );
        
        String disputeClassInstanceId = storeSignal(debitSignalRequest.getTransactionId(), 
                                                   signalMetadata, 
                                                   DisputeClassInstanceMetaKey.DEBIT_SIGNAL);
        
        log.info("Debit signal processed successfully for transaction ID: {}", debitSignalRequest.getTransactionId());
        
        return DebitSignalResponse.builder()
            .transactionId(debitSignalRequest.getTransactionId())
            .signalType(debitSignalRequest.getSignalType())
            .disputeWorkflowInstanceId(disputeClassInstanceId)
            .message("Debit signal processed successfully")
            .build();
    }

    private String storeSignal(String transactionId, SignalMetadata signalMetadata, DisputeClassInstanceMetaKey metaKey) {
        List<DisputeEntity> disputeEntities = disputeRepository.fetchByTransactionId(transactionId);
        String disputeId = disputeEntities.get(0).getDisputeId();
        
        List<DisputeClassInstanceEntity> disputeInstances = disputeClassInstanceRepository.search(
            DisputeInstanceSearchRequest.builder().build(), disputeId);
        DisputeClassInstanceEntity disputeInstance = disputeInstances.get(0);
        
        Optional<DisputeClassInstanceMetaEntity> existingMeta = disputeClassInstanceMetaRepository.findByMetaKey(
            disputeInstance.getWorkflowClassId(), 
            disputeInstance.getDisputeClassInstanceId(), 
            metaKey);

        List<SignalMetadata> signals = new ArrayList<>();
        
        if (existingMeta.isPresent()) {
            try {
                signals = MapperUtils.deserialize(existingMeta.get().getMetaValue(), 
                                                new TypeReference<List<SignalMetadata>>() {});
                
                signals.removeIf(signal -> signal.getTransactionId().equals(transactionId));
            } catch (Exception e) {
                log.warn("Error parsing existing signals, creating new list", e);
                signals = new ArrayList<>();
            }
        }
        
        signals.add(signalMetadata);
        String serializedSignals = MapperUtils.serializeToString(signals);
        
        if (existingMeta.isPresent()) {
            disputeClassInstanceMetaRepository.updateMetaValue(
                disputeInstance.getWorkflowClassId(),
                disputeInstance.getDisputeClassInstanceId(),
                metaKey,
                serializedSignals);
        } else {
            DisputeClassInstanceMetaEntity newMeta = DisputeClassInstanceMetaEntity.builder()
                .disputeClassInstanceId(disputeInstance.getDisputeClassInstanceId())
                .workflowClassId(disputeInstance.getWorkflowClassId())
                .metaKey(metaKey)
                .metaValue(serializedSignals)
                .partitionId(PenaltyDBUtils.getMonthBasedPartition(new Date()))
                .build();
            disputeClassInstanceMetaRepository.save(newMeta);
        }
        
        return disputeInstance.getDisputeClassInstanceId();
    }
}
