package com.phonepe.central.dms.server.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.central.dispute.context.DisputeClassInstanceMetaKey;
import com.phonepe.central.dms.server.mariadb.entities.DisputeClassInstanceMetaEntity;
import com.phonepe.central.dms.server.mariadb.repository.DisputeClassInstanceMetaRepository;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class DisputeClassInstanceMetaService {

    private final DisputeClassInstanceMetaRepository disputeClassInstanceMetaRepository;

    public void saveNewDisputeRegisterMeta(@NotBlank String workflowClassId, @NotBlank String disputeClassInstanceId, @Valid JsonNode metaData) {
        log.info("Inserting new dispute register meta for disputeClassInstanceId: {}, workflowClassId: {}, metaData: {}", disputeClassInstanceId, workflowClassId, metaData);
        DisputeClassInstanceMetaEntity metaEntity = DisputeClassInstanceMetaEntity.builder()
                .disputeClassInstanceId(disputeClassInstanceId)
                .workflowClassId(workflowClassId)
                .metaKey(DisputeClassInstanceMetaKey.DISPUTE_REGISTER_REQUEST)
                .metaValue(MapperUtils.serializeToString(metaData))
                .build();
        disputeClassInstanceMetaRepository.save(metaEntity);
    }
}
