package com.phonepe.central.dms.server.mariadb.repository;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.central.dms.server.mariadb.entities.DisputeEntity;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.RelationalDaoCrudRepository;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import io.appform.dropwizard.sharding.dao.RelationalDao;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Singleton
public class DisputeRepository extends RelationalDaoCrudRepository<DisputeEntity> {

    private static final Integer MAX_PAGE_SIZE = 2000;

    @Inject
    public DisputeRepository(RelationalDao<DisputeEntity> relationalDao) {
        super(relationalDao);
    }

    public List<DisputeEntity> fetchBy(final String tenantId, final String transactionId) {
        try {
            final var detachedCriteria = DetachedCriteria.forClass(DisputeEntity.class);
            detachedCriteria.add(Restrictions.eq(DisputeEntity.Fields.tenantId, tenantId));
            detachedCriteria.add(Restrictions.eq(DisputeEntity.Fields.transactionId, transactionId));
            return this.relationalDao.select(tenantId, detachedCriteria, 0, MAX_PAGE_SIZE);
        } catch (Exception exception) {
            log.error("Exception in getting the disputes for tenantID {} , transactionId {} is ", tenantId, transactionId, exception);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DB_ERROR, exception);
        }

    }

    public List<DisputeEntity> fetchByTransactionId(final String transactionId) {
        try {
            final var detachedCriteria = DetachedCriteria.forClass(DisputeEntity.class);
            detachedCriteria.add(Restrictions.eq(DisputeEntity.Fields.transactionId, transactionId));
            return this.relationalDao.scatterGather(detachedCriteria, 0, MAX_PAGE_SIZE);
        } catch (Exception exception) {
            log.error("Exception in getting the disputes for transactionId {} is ", transactionId, exception);
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DB_ERROR, exception);
        }
    }
}
