package com.phonepe.central.dms.server.validation.dispute_instance.create;

import com.phonepe.central.dispute.request.disputeinstance.DisputeRegisterRequest;
import com.phonepe.central.workflow.request.workflow.WorkflowClassSearchRequest;
import com.phonepe.central.workflow.response.workflow.WorkflowClassResponse;
import com.phonepe.central.workflow.server.service.WorkflowClassService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */

@Slf4j
public class ActiveWorkflowDisputeInstanceValidation extends DisputeInstanceCreationRequestValidation<DisputeRegisterRequest> {

    private final WorkflowClassService workflowClassService;

    public ActiveWorkflowDisputeInstanceValidation(WorkflowClassService workflowClassService, DisputeInstanceCreationRequestValidation<DisputeRegisterRequest> next) {
        super(next);
        this.workflowClassService = workflowClassService;
    }

    @Override
    public boolean isCreateInstanceValid(DisputeRegisterRequest input) {
        log.info("Validating for active workflow class validation for input {}", input);
        List<WorkflowClassResponse> workflowClassResponse = workflowClassService.searchWorkflowClass(WorkflowClassSearchRequest.builder().workflowClassId(input.getWorkflowClassId()).build());
        if (CollectionUtils.isNotEmpty(workflowClassResponse) && workflowClassResponse.stream().anyMatch(WorkflowClassResponse::isWorkflowIsActive)) {
            return true;
        }
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.WORKFLOW_CLASS_NOT_ACTIVE, "Workflow class is not active for input " + input);
    }
}
