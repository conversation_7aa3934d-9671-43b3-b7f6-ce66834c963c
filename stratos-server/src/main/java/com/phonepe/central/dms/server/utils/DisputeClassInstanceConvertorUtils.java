package com.phonepe.central.dms.server.utils;

import com.phonepe.central.dispute.Dispute;
import com.phonepe.central.dispute.DisputeState;
import com.phonepe.central.dispute.request.disputeinstance.DisputeRegisterRequest;
import com.phonepe.central.dispute.response.disputeinstance.DisputeInstanceResponse;
import com.phonepe.central.dms.server.mariadb.entities.DisputeClassInstanceEntity;
import com.phonepe.central.dms.server.mariadb.entities.DisputeClassInstanceMetaEntity;
import com.phonepe.central.dms.server.mariadb.entities.DisputeEntity;
import com.phonepe.central.stratos.penalty.server.util.PenaltyDBUtils;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import lombok.experimental.UtilityClass;

import java.util.Date;

@UtilityClass
public class DisputeClassInstanceConvertorUtils {

    public static final String DISPUTE_CLASS_INSTANCE_ID_PREFIX = "DCI";
    public static final String DISPUTE_ID_PREFIX = "D";
    private static final String DELIMITER = "_";


    public DisputeClassInstanceEntity from(final DisputeRegisterRequest disputeRegisterRequest, String disputeId) {
        if (disputeRegisterRequest != null) {
            return DisputeClassInstanceEntity.builder()
                    .workflowClassId(disputeRegisterRequest.getWorkflowClassId())
                    .disputeClassInstanceId(IdGenerator.generate(DISPUTE_CLASS_INSTANCE_ID_PREFIX)
                            .getId())
                    .workflowClassInstanceId("EMPTY")
                    .disputeId(disputeId)
                    .disputedAmount(disputeRegisterRequest.getDisputedAmount())
                    .raisedAgainstByUserId(disputeRegisterRequest.getRaisedAgainstByUserId())
                    .raisedAgainstByUserType(disputeRegisterRequest.getRaisedAgainstByUserType())
                    .state(DisputeState.OPEN)
                    .build();
        }
        return null;
    }

    public DisputeEntity from(final DisputeRegisterRequest disputeRegisterRequest) {
        if (disputeRegisterRequest != null) {
            return DisputeEntity.builder()
                    .tenantId(disputeRegisterRequest.getTenantId())
                    .disputeId(IdGenerator.generate(DISPUTE_ID_PREFIX)
                            .getId())
                    .name(disputeRegisterRequest.getTransactionType() + DELIMITER + disputeRegisterRequest.getTransactionId())
                    .description(disputeRegisterRequest.getTransactionType() + DELIMITER + disputeRegisterRequest.getTransactionId())
                    .transactionId(disputeRegisterRequest.getTransactionId())
                    .transactionAmount(disputeRegisterRequest.getTransactionAmount())
                    .partitionId(PenaltyDBUtils.getMonthBasedPartition(new Date()))
                    .state(DisputeState.OPEN)
                    .build();
        }
        return null;
    }

    public static Dispute convertToDisputeResponse(DisputeEntity disputeEntity) {
        if (disputeEntity != null) {
            return Dispute.builder().build();
        }
        return null;
    }

    public static DisputeInstanceResponse convertToDisputeResponse(DisputeClassInstanceEntity disputeClassInstanceEntity) {
        if (disputeClassInstanceEntity != null) {
            return DisputeInstanceResponse.builder()
                    .disputeId(disputeClassInstanceEntity.getDisputeId())
                    .disputeClassInstanceId(disputeClassInstanceEntity.getDisputeClassInstanceId())
                    .state(disputeClassInstanceEntity.getState())
                    .raisedAgainstByUserId(disputeClassInstanceEntity.getRaisedAgainstByUserId())
                    .raisedAgainstByUserType(disputeClassInstanceEntity.getRaisedAgainstByUserType())
                    .raisedAt(disputeClassInstanceEntity.getRaisedAt())
                    .createAt(disputeClassInstanceEntity.getCreatedAt())
                    .workflowClassId(disputeClassInstanceEntity.getWorkflowClassId())
                    .workflowInstanceId(disputeClassInstanceEntity.getWorkflowClassInstanceId())
                    .build();
        }
        return null;
    }




}