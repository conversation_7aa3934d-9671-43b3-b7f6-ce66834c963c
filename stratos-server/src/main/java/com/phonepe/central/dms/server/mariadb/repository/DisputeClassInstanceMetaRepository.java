package com.phonepe.central.dms.server.mariadb.repository;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.central.dms.server.mariadb.entities.DisputeClassInstanceMetaEntity;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.RelationalDaoCrudRepository;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import io.appform.dropwizard.sharding.dao.RelationalDao;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Singleton
public class DisputeClassInstanceMetaRepository extends RelationalDaoCrudRepository<DisputeClassInstanceMetaEntity> {

    private static final Integer MAX_PAGE_SIZE = 2000;


    @Inject
    public DisputeClassInstanceMetaRepository(RelationalDao<DisputeClassInstanceMetaEntity> relationalDao) {
        super(relationalDao);
    }

    public List<DisputeClassInstanceMetaEntity> search(final String workflowClassId, final String disputeClassInstanceId) {
        try {
            final var detachedCriteria = DetachedCriteria.forClass(DisputeClassInstanceMetaEntity.class);
            detachedCriteria.add(Restrictions.eq(DisputeClassInstanceMetaEntity.Fields.workflowClassId, workflowClassId));
            detachedCriteria.add(Restrictions.eq(DisputeClassInstanceMetaEntity.Fields.disputeClassInstanceId, disputeClassInstanceId));
            return this.relationalDao.select(workflowClassId, detachedCriteria, 0, MAX_PAGE_SIZE);
        } catch (Exception exception) {
            log.error("Exception in searching for meta data for dispute classInstanceId");
            throw DisputeExceptionUtil.propagate(StratosErrorCodeKey.DB_ERROR, exception);
        }
    }

}
