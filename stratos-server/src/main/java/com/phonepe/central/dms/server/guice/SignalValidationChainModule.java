package com.phonepe.central.dms.server.guice;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.phonepe.central.dispute.context.DisputeClassInstanceMetaKey;
import com.phonepe.central.dispute.request.disputeinstance.signal.credit.CreditSignalRequest;
import com.phonepe.central.dispute.request.disputeinstance.signal.debit.DebitSignalRequest;
import com.phonepe.central.dms.server.mariadb.repository.DisputeClassInstanceMetaRepository;
import com.phonepe.central.dms.server.mariadb.repository.DisputeClassInstanceRepository;
import com.phonepe.central.dms.server.mariadb.repository.DisputeRepository;
import com.phonepe.central.dms.server.validation.signal.DisputeExistenceValidation;
import com.phonepe.central.dms.server.validation.signal.DuplicateSignalValidation;
import com.phonepe.central.dms.server.validation.signal.SignalValidation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class SignalValidationChainModule extends AbstractModule {

    @Provides
    @Singleton
    public SignalValidation<CreditSignalRequest> creditSignalValidation(
            final DisputeRepository disputeRepository,
            final DisputeClassInstanceRepository disputeClassInstanceRepository,
            final DisputeClassInstanceMetaRepository disputeClassInstanceMetaRepository) {
        
        return new DisputeExistenceValidation<>(disputeRepository,
                new DuplicateSignalValidation<>(disputeRepository,
                        disputeClassInstanceRepository,
                        disputeClassInstanceMetaRepository,
                        DisputeClassInstanceMetaKey.CREDIT_SIGNAL,
                        null));
    }

    @Provides
    @Singleton
    public SignalValidation<DebitSignalRequest> debitSignalValidation(
            final DisputeRepository disputeRepository,
            final DisputeClassInstanceRepository disputeClassInstanceRepository,
            final DisputeClassInstanceMetaRepository disputeClassInstanceMetaRepository) {
        
        return new DisputeExistenceValidation<>(disputeRepository,
                new DuplicateSignalValidation<>(disputeRepository,
                        disputeClassInstanceRepository,
                        disputeClassInstanceMetaRepository,
                        DisputeClassInstanceMetaKey.DEBIT_SIGNAL,
                        null));
    }
}
