package com.phonepe.central.dms.server.validation.dispute_instance.create;

import com.phonepe.central.dispute.request.disputeinstance.DisputeRegisterRequest;
import com.phonepe.central.workflow.server.validation.Validation;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
public abstract class DisputeInstanceCreationRequestValidation<T extends DisputeRegisterRequest> implements
        Validation<T> {

    private DisputeInstanceCreationRequestValidation<T> next;

    @Override
    public boolean validate(T input) {
        if (next == null) {
            return this.isCreateInstanceValid(input);
        }
        return this.isCreateInstanceValid(input) && next.validate(input);
    }

    public abstract boolean isCreateInstanceValid(T input);
}
