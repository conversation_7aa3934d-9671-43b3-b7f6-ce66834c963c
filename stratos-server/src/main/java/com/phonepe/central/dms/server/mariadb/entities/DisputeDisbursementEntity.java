package com.phonepe.central.dms.server.mariadb.entities;

import com.phonepe.central.dispute.disbursement.DisputeDisbursementMode;
import com.phonepe.central.dispute.disbursement.DisputeDisbursementState;
import com.phonepe.central.dispute.user.UserType;
import com.phonepe.services.ppo.core.storage.mariadb.Sharded;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "dms_dispute_disbursement", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"workflow_class_id", "dispute_class_instance_id",
                "disbursement_transaction_id"}), @UniqueConstraint(columnNames = {"disbursement_id"})}, indexes = {
        @Index(name = "idx_dms_disbursement_id", columnList = "workflow_class_id,disbursement_id"),
        @Index(name = "idx_dms_disbursement_instance_id", columnList = "workflow_class_id,dispute_class_instance_id")})
@FieldNameConstants
public class DisputeDisbursementEntity implements Sharded {

    private static final long serialVersionUID = -6548024317040241952L;

    @Id
    @Column(name = "id", unique = true, insertable = false, updatable = false, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @NotBlank
    @Column(name = "workflow_class_id", nullable = false)
    private String workflowClassId;

    @NotBlank
    @Column(name = "dispute_class_instance_id", nullable = false)
    private String disputeClassInstanceId;


    @NotBlank
    @Column(name = "disbursement_id", nullable = false)
    private String disbursementId;

    @NotBlank
    @Column(name = "disbursement_transaction_id", nullable = false)
    private String disbursementTransactionId;

    @NotNull
    @Column(name = "expected_disbursement_amount", nullable = false)
    private Long expectedDisbursementAmount;

    @NotNull
    @Column(name = "actual_disbursement_amount", nullable = false)
    private Long actualDisbursedAmount;


    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "state", nullable = false)
    private DisputeDisbursementState state;


    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "disbursement_mode", nullable = false)
    private DisputeDisbursementMode disbursementMode;


    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "disbursement_to_user_type", nullable = false)
    private UserType disbursementToUserType;

    @NotBlank
    @Column(name = "disbursement_to_user_id", nullable = false)
    private String disbursementToUserId;

    @Column(name = "partition_id", nullable = false, updatable = false)
    private int partitionId;


    @CreationTimestamp
    @Column(name = "created_at", columnDefinition = "datetime(3) DEFAULT current_timestamp", updatable = false, nullable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", columnDefinition = "datetime(3) DEFAULT current_timestamp ON UPDATE current_timestamp", nullable = false)
    private LocalDateTime updatedAt;


    @Override
    public String getKey() {
        return workflowClassId;
    }


}
