package com.phonepe.central.workflow.server.util;

import com.google.inject.Singleton;
import com.phonepe.central.workflow.context.AcceptContext;
import com.phonepe.central.workflow.context.CommentContext;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Singleton
@NoArgsConstructor
public class ContextValueVisitor implements ContextTypeVisitor<Class<?>>{

    @Override
    public Class<?> visitCommentContext() {
        return CommentContext.class;
    }

    @Override
    public Class<?> visitAcceptAmountContext() {
        return AcceptContext.class;
    }

    @Override
    public Class<?> visitAccountingEventContext() {
        return String.class;
    }
}
