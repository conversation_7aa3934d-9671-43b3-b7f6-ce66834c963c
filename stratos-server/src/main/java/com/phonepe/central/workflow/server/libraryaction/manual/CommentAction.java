package com.phonepe.central.workflow.server.libraryaction.manual;

import com.phonepe.central.workflow.action.libraryaction.param.ActionParam;
import com.phonepe.central.workflow.action.response.context.ActionResponseContext;
import com.phonepe.central.workflow.action.response.context.impl.SuccessfulActionResponseContext;
import com.phonepe.central.workflow.server.mariadb.entities.WorkflowClassInstanceMetaEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
@AllArgsConstructor
public class CommentAction extends ManualTriggerAction {

    @Override
    public ActionResponseContext execute(
            ActionParam param,
            WorkflowClassInstanceMetaEntity stageActionContext) {
        log.info("Return success if state transition context is of type EmptyContext");
        //TODO add validation for transition context passed
        if(true){
            //TODO: persist comment and return success
            return SuccessfulActionResponseContext.builder().build();
        }
        return null; // Return appropriate response context
    }
}
