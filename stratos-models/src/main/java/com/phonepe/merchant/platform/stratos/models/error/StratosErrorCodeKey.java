package com.phonepe.merchant.platform.stratos.models.error;

import com.phonepe.error.configurator.model.model.IErrorKey;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum StratosErrorCode<PERSON>ey implements IErrorKey {
    SUCCESS("200"),
    INTERNAL_SERVER_ERROR("500"),
    SERIALIZATION_ERROR("500"),
    DESERIALIZATION_ERROR("500"),
    DB_ERROR("500"),
    COMMUNICATION_ERROR("500"),
    AEROSPIKE_WRITE_ERROR("500"),
    AEROSPIKE_READ_ERROR("500"),
    AEROSPIKE_DELETE_ERROR("500"),
    INIT_ERROR("500"),
    PAYMENTS_CLIENT_ERROR("500"),
    FEED_PUBLISH_ERROR("500"),
    QUEUE_ERROR("500"),
    TRANSFORMATION_ERROR("500"),
    HYSTRIX_TIMEOUT("500"),

    AUTH_ERROR("401"),
    OPERATION_NOT_ALLOWED("405"),
    WRONG_INPUT_ERROR("400"),
    INVALID_PAGINATION_PARAMETERS("400"),
    INVALID_MERCHANT_ID("401"),
    INVALID_FILE("400"),

    INVALID_ROW("400"),
    DISPUTE_NOT_FOUND("404"),
    DISPUTE_WORKFLOW_NOT_FOUND("404"),
    EVIDENCE_NOT_FOUND("404"),
    TRANSITION_NOT_ALLOWED("409"),
    TRANSITION_LOCK_KEY_NOT_FOUND("404"),
    UNABLE_TO_ACQUIRE_TRANSITION_LOCK("409"),
    DISPUTE_STATE_MACHINE_NOT_FOUND("404"),
    DISPUTE_DATA_MISMATCH("400"),
    INVALID_DISPUTE_AMOUNT("400"),
    INVALID_ACCEPTED_AMOUNT("400"),
    INVALID_CONTESTED_AMOUNT("400"),
    INVALID_CREDIT_AMOUNT("400"),
    INVALID_DEBIT_AMOUNT("400"),
    FEED_SCHEMA_PARAMS_NOT_FOUND("404"),
    EMPTY_COMMENT_NOT_ALLOWED("400"),
    COMMENT_LENGTH_EXCEEDED_LIMIT("400"),
    RAISED_ACCOUNTING_EVENT_NOT_FOUND("404"),
    INVALID_UPI_ID("400"),
    DUPLICATE_FILE_NAME("400"),
    FILE_LOCK("400"),
    INVALID_FILE_NAME_FORMAT("400"),
    FILE_NOT_FOUND("404"),
    UNSUPPORTED_TRANSACTION("400"),
    UNSUPPORTED_DISPUTE_FILE("400"),
    INVALID_TRANSITION("400"),
    INVALID_TRANSACTION("400"),
    INVALID_TRANSACTION_AMOUNT("400"),
    INVALID_END_DATE("400"),
    PREVIOUS_DISPUTE_WORKFLOW_NOT_ENDED("400"),
    INVALID_CHARGEBACK_REASON_CODE("400"),
    INVALID_PAYMENTS_ID("400"),
    DUPLICATE_CHARGEBACK("400"),
    ID_GENERATION_FAILED("400"),
    UNSUPPORTED_DISPUTE_STAGE("400"),
    UNSUPPORTED_DISPUTE_TYPE("400"),
    INVALID_UDIR_COMPLAINT("400"),
    ROW_NOT_FOUND("400"),
    FIRST_LEVEL_DISPUTE_NOT_FOUND("404"),

    UNSUPPORTED_PAYEE("400"),
    INVALID_DISPUTE_TYPE("400"),
    ENUM_CLASS_NULL("400"),
    FILTER_NOT_SUPPORTED("400"),
    INVALID_REFUND_ID("400"),

    //TOA Errors
    DUPLICATE_TOA("400"),
    INVALID_TOA("400"),
    PAYMENT_INITIATION_FAILED("400"),
    PAYMENT_MERCHANT_TXN_STATUS_CHECK_FAILED("400"),
    TOA_RETRY_NOT_SUPPORTED("400"),
    TOA_COMPLETE_EXTERNALLY_NOT_SUPPORTED("400"),
    TOA_INVALID_RE_INITIATE("400"),
    KS_ALREADY_ENGAGED("400"),
    KS_NOT_ENGAGED("400"),
    NOT_HUMAN_USER("400"),
    UNSUPPORTED_PAYMENT_DESTINATION("400"),
    FETCH_MERCHANT_ID_ERROR("500"),
    MIDDLE_MAN_MERCHANT_DATA_NULL("500"),
    EDC_TRANSACTION_DETAIL_ERROR("500"),
    EDC_CLIENT_ERROR("500"),
    INVALID_KRATOS_ACTION("500"),
    RECONCILE_FAILURE("500"),
    UNSUPPORTED_DISPUTE_CREATION("400"),
    UNSUPPORTED_DISPUTE("400"),
    UNSUPPORTED_DISPUTE_VALIDATION("400"),
    DISPUTE_METADATA_NOT_FOUND("404"),
    PRE_ARB_NOT_SUPPORTED("400"),
    UNABLE_TO_ENRICH_CHARGEBACK("400"),
    TTL_BREACHED("400"),

    //Kaizen Errors
    KAIZEN_ACTION_DETAIL_NOT_FOUND("404"),
    KAIZEN_WORKFLOW_NOT_FOUND("404"),

    VALIDATION_FAILURE("400"),
    FIRST_LEVEL_NOT_IN_TERMINAL_STATE("400"),
    KRATOS_ACTOR_FAILURE("500"),
    EDC_ACTOR_FAILURE("500"),
    // Warden related errors:
    MAKER_REQUEST_FAILED("500"),
    CALLBACK_PROCESSING_FAILED("500"),
    PROBABLE_REGISTER_ERROR("500"),
    PROBABLE_ALREADY_PRESENT("400"),

    PROBABLE_ALREADY_PRESENT_WITH_PREVIOUS_VERSION("400"),

    PROBABLE_RECONCILE_ERROR("500"),
    PROBABLE_GET_ERROR("500"),
    PENALTY_GET_ERROR("500"),
    PENALTY_CLASS_ALREADY_PRESENT("400"),

    PENALTY_ALREADY_PRESENT("400"),

    PENALTY_ALREADY_PRESENT_WITH_PREVIOUS_CLASS_VERSION("400"),
    UNABLE_TO_CREATE_PENALTY_CLASS("500"),
    UNABLE_TO_UPDATE_PENALTY_CLASS("500"),
    UNABLE_TO_GET_PENALTY_SCHEDULE_TIME("500"),
    PENALTY_CLASS_CRITERIA_NOT_MET("500"),
    PENALTY_DISBURSEMENT_ERROR("500"),
    PENALTY_CLASS_NOT_PRESENT("404"),
    NOTIFICATION_SERVICE_ERROR("500"),
    NOTIFICATION_SENDING_ERROR("500"),
    SERVICE_CLIENT_ERROR("500"),
    PENALTY_CLASS_NAME_ALREADY_EXISTS("302"),
    PENALTY_TENANT_INFO_LIST_IS_EMPTY("403"),
    PENALTY_TENANT_NOT_AUTHORIZED("403"),
    PENALTY_RECONCILE_ERROR("500"),
    PENALTY_CLASS_ACTIVATION_FAILED("500"),
    PENALTY_CLASS_NOT_FOUND("404"),
    PENALTY_CLASS_CLIENT_RESOLUTION_CONFIG_NOT_FOUND("404"),
    PENALTY_CLASS_DUPLICATION_FAILED("500"),
    PENALTY_CLASS_ACTIVE_UPDATE("500"),
    TEMPLATE_NOT_FOUND("500"),
    FRA_REQUEST_CREATION_FAILED("500"),
    ESCALATION_NOT_FOUND("404"),
    ESCALATION_PUBLISH_ERROR("500"),
    UNABLE_TO_CREATE_TENANT("500"),
    UNABLE_TO_GET_TENANT("500"),
    UNABLE_TO_UPDATE_TENANT("500"),
    UNABLE_TO_DELETE_TENANT("500"),
    TENANT_ALREADY_EXISTS("400"),
    TENANT_NOT_FOUND("404"),
    DISPUTE_CREATION_LOCK("400"),
    NON_EXTERNAL_MERCHANT_TRANSACTION("400"),
    UNABLE_TO_CREATE_ACTION("500"),
    ACTION_NOT_FOUND("404"),
    INVALID_ACTION_PARAM("400"),
    ACTION_ALREADY_EXISTS("400"),
    INVALID_ACTION_TYPE("400"),
    PENALTY_CLIENT_RESOLUTION_ERROR("500"),
    FRAUD_CHARGEBACK_NOT_ALLOWED("400"),
    PAYMENT_NOT_IN_COMPLETED_STATE("400"),
    STAGE_NOT_FOUND("404"),
    STAGE_CREATION_FAILED("500"),
    STAGE_UPDATION_FAILED("500"),
    STAGE_ALREADY_EXISTS("409"),
    UNABLE_TO_CREATE_WORKFLOW_CLASS("500"),
    UNABLE_TO_UPDATE_WORKFLOW_CLASS("500"),
    WORKFLOW_CLASS_NOT_FOUND("404"),
    WORKFLOW_INSTANCE_ALREADY_EXISTS("400"),
    WORKFLOW_CLASS_IS_NOT_IN_CREATED_STATE("400"),
    WORKFLOW_CLASS_NAME_ALREADY_EXISTS("302"),
    UNABLE_TO_ENRICH_WORKFLOW_CLASS("500"),
    UNABLE_TO_CREATE_WORKFLOW_INSTANCE("500"),
    TARGET_ID_NOT_FOUND("404"),
    STAGE_EXECUTION_FAILED("500"),
    WARDEN_WORKFLOW_FETCH_ERROR("500"),
    WARDEN_REVIEW_CONTEXT_NULL_ERROR("500"),
    WARDEN_INSTANCE_NOT_APPROVED("500"),
    WARDEN_WORKFLOW_CREATION_ERROR("500"),
    PGT_TRANSACTION_DETAIL_ERROR("500"),
    RETRY_NOT_ALLOWED("400"),
    WORKFLOW_CLASS_NOT_ACTIVE("400"),
    DISPUTE_INSTANCE_ALREADY_EXIST("500"),
    DISPUTE_REGISTRATION_ERROR("500"),
    WORKFLOW_CLASS_INSTANCE_CONTEXT_NOT_FOUND("400"),
    INVALID_CONTEXT("400");

    private String key;

    StratosErrorCodeKey(String key) {
        this.key = key;
    }

    @Override
    public String getKey() {
        return this.key;
    }

    @Override
    public List<String> getKeys() {
        return Arrays.stream(values()).map(Enum::toString).collect(Collectors.toList());
    }
}
