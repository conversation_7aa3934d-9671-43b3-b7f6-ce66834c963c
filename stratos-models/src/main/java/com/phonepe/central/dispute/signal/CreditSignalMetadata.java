package com.phonepe.central.dispute.signal;

import com.phonepe.central.dispute.request.disputeinstance.signal.credit.CreditSignalType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CreditSignalMetadata extends SignalMetadata {
    
    @NotNull
    private CreditSignalType creditSignalType;
    
    public CreditSignalMetadata(String transactionId, SignalStatus status, String payload, CreditSignalType creditSignalType) {
        super(transactionId, status, "CREDIT", payload);
        this.creditSignalType = creditSignalType;
    }
}
