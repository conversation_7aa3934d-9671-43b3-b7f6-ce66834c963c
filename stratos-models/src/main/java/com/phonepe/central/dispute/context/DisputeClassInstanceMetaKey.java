package com.phonepe.central.dispute.context;

/**
 * <AUTHOR>
 */
/**
 * Enum for metaKey values representing different metadata categories.
 */
public enum DisputeClassInstanceMetaKey {
    NPCI_RECEIVED_SIGNAL,  // Metadata related to NPCI signals received
    NPCI_ACKNOWLEDGED_SIGNAL,  // Metadata related to NPCI signals acknowledged
    NPCI_CREDITED_SIGNAL,  // Metadata related to NPCI signals credited
    NPCI_DEBITED_SIGNAL,  // Metadata related to NPCI signals debited
    DISBURSEMENT,  // Metadata related to disbursement
    RECOVERY,  // Metadata related to recovery
    PENALTY,  // Metadata related to penalty applied to the dispute
    CHARGES,  // Metadata related to charges like CF/PF
    CLASS_INSTANCE_CLOSURE_DETAILS,  // Metadata related to class instance closure details
    DISPUTE_REGISTER_REQUEST,  // Metadata related to dispute registration request
    CREDIT_SIGNAL,  // Metadata related to credit signals received
    DEBIT_SIGNAL,  // Metadata related to debit signals received
}
