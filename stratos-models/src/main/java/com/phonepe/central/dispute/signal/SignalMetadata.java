package com.phonepe.central.dispute.signal;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "signalType", visible = true)
@JsonSubTypes({
    @JsonSubTypes.Type(value = CreditSignalMetadata.class, name = "CREDIT"),
    @JsonSubTypes.Type(value = DebitSignalMetadata.class, name = "DEBIT")
})
public abstract class SignalMetadata {
    
    @NotBlank
    private String transactionId;
    
    @NotNull
    private SignalStatus status;
    
    @NotBlank
    private String signalType;
    
    @NotBlank
    private String payload;
}
