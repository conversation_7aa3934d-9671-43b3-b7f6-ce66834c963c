package com.phonepe.central.dispute.request.disputeinstance;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.fasterxml.jackson.databind.JsonNode;
import com.phonepe.central.dispute.request.disputeinstance.impl.UPIDisputeRegisterRequest;
import com.phonepe.central.dispute.user.UserType;
import com.phonepe.merchant.platform.stratos.models.commons.TransactionType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ToString
@SuperBuilder
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "transactionType", visible = true)
@JsonSubTypes(value = {@Type(value = UPIDisputeRegisterRequest.class, name = TransactionType.UPI_TRANSACTION_TEXT),})
@NoArgsConstructor(force = true)
@AllArgsConstructor
public abstract class DisputeRegisterRequest {

    @NotNull
    private TransactionType transactionType;

    @NotBlank
    private String workflowClassId;

    @NotEmpty
    private String tenantId;

    @NotNull
    @Min(1)
    private Long disputedAmount;

    @NotBlank
    private String transactionId;

    @Min(1)
    private long transactionAmount;

    @NotBlank
    private String raisedAgainstByUserId;

    @NotNull
    private UserType raisedAgainstByUserType;

    @Valid
    private JsonNode metaData;

}
