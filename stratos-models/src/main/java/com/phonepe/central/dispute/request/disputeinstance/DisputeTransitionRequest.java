package com.phonepe.central.dispute.request.disputeinstance;

import com.fasterxml.jackson.databind.JsonNode;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DisputeTransitionRequest {

    @NotBlank
    private String disputeClassInstanceId;

    @NotBlank
    private String workflowClassId;

    @NotBlank
    private String targetTransitionId;

    @NotNull
    @Valid
    private JsonNode transitionContext;

}
