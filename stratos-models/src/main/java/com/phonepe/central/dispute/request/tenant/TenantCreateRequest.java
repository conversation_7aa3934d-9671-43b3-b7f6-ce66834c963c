package com.phonepe.central.dispute.request.tenant;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.*;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@EqualsAndHashCode
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type")
@JsonSubTypes({@JsonSubTypes.Type(name = TenantCreationType.BASE_TENANT_TEXT, value = BaseTenantCreateRequest.class),
        @JsonSubTypes.Type(name = TenantCreationType.CATEGORY_TENANT_TEXT, value = CategoryTenantCreateRequest.class),
        @JsonSubTypes.Type(name = TenantCreationType.SUB_CATEGORY_TENANT_TEXT, value = SubCategoryTenantCreateRequest.class),
        @JsonSubTypes.Type(name = TenantCreationType.WORKFLOW_CLASS_LEVEL_TENANT_TEXT, value = WorkflowClassLevelTenantCreateRequest.class),})
@SuperBuilder
public abstract class TenantCreateRequest {

    private final TenantCreationType type;

    @NotBlank
    private String name;

    private String description;

    @NotEmpty(message = "At least one email is required")
    @Size(max = 2, message = "More than 2 emails  are not allowed")
    private List<@NotBlank @Email @Pattern(regexp = "^[A-Za-z0-9._%+-]+@phonepe\\.com$", message = "Email must be a valid @phonepe.com address") String> emailIds;


    public abstract <T> T accept(TenantCreateRequestVisitor<T> visitor);

    public interface TenantCreateRequestVisitor<T> {

        T visit(final BaseTenantCreateRequest createRequest);

        T visit(final CategoryTenantCreateRequest createRequest);
        T visit(final SubCategoryTenantCreateRequest createRequest);
        T visit(final WorkflowClassLevelTenantCreateRequest createRequest);

    }

}
