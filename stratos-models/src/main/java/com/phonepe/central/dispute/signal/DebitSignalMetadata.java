package com.phonepe.central.dispute.signal;

import com.phonepe.central.dispute.request.disputeinstance.signal.debit.DebitSignalType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DebitSignalMetadata extends SignalMetadata {
    
    @NotNull
    private DebitSignalType debitSignalType;
    
    public DebitSignalMetadata(String transactionId, SignalStatus status, String payload, DebitSignalType debitSignalType) {
        super(transactionId, status, "DEBIT", payload);
        this.debitSignalType = debitSignalType;
    }
}
