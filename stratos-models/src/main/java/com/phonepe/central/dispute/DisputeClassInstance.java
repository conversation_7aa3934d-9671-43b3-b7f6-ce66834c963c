package com.phonepe.central.dispute;

import com.phonepe.central.dispute.context.type.DisputeTypeDetail;
import com.phonepe.central.dispute.user.UserType;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DisputeClassInstance {

    @NotBlank
    private String disputeId;

    @NotBlank
    private String workflowClassId;

    @NotBlank
    private String disputeClassInstanceId;

    @NotBlank
    private String workflowInstanceId;


    @NotNull
    private Long disputedAmount;

    @NotNull
    private Long acceptedAmount;

    @NotNull
    private DisputeTypeDetail disputeTypeDetail;

    @NotBlank
    private String raisedBy;

    @NotNull
    private UserType raisedUserType;

    @NotBlank
    private String raisedAgainstBy;

    @NotNull
    private UserType raisedAgainstUserType;


    @NotNull
    private DisputeState disputeState;

    @NotNull
    private Date raisedAt;

    @NotNull
    private Date closedAt;

    @NotNull
    private Date createdAt;

    @NotNull
    private Date updatedAt;


}
