package com.phonepe.central.dispute.tenant;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.phonepe.central.workflow.workflow.WorkflowLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Tenant {

    @NotBlank
    private String tenantId;

    @NotBlank
    private String name;

    private String category;

    private String subCategory;

    private WorkflowLevel workflowClassLevel;

    private Tenant parent;

    private List<Tenant> children;

    private String description;

    @NotEmpty
    private Set<@NotBlank String> emailIds;

    @NotNull
    private TenantState state;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime createdAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime updatedAt;

    @JsonIgnore
    public Boolean isChildPresent() {
        return !this.children.isEmpty();
    }

    @JsonIgnore
    public String getParentName() {
        return parent != null ? parent.getName() : "EMPTY";
    }

}